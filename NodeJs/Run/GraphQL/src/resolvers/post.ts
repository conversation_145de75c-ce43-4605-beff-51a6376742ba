import {
    CompleteContentPostResolvers,
    LimitedContentPostResolvers,
    PostAssetResolvers,
    PostGjirafaAssetResolvers,
    PostGjirafaLivestreamAssetResolvers,
    PostResolvers,
    PostSortFields,
} from '../generated/resolvers-types'
import { turndownService } from '../common/util'
import { unauthorizedError } from './utils'

export const completeContentPostResolver: CompleteContentPostResolvers = {
    comments: async ({ id }, { first, sortDirection }, { dataSources }) => {
        const { comments, pagination } = await dataSources.postAPI.getComments(id, { first, sortDirection })
        return {
            nodes: comments,
            pageInfo: pagination,
        }
    },
    markdown: async ({ textHtml }) => {
        if (!textHtml) return null

        return turndownService.turndown(textHtml)
    },
}

export const limitedContentPostResolvers: LimitedContentPostResolvers = {}

export const postResolvers: PostResolvers = {
    __resolveType: (post) => {
        if (post.fullAssets) {
            return 'CompleteContentPost'
        } else {
            return 'LimitedContentPost'
        }
    },
    user: async ({ userId }, _, { dataSources }) => {
        return dataSources.userAPI.getUser(userId)
    },

    posts: async ({ userId, publishedAt }, { first, direction }, { dataSources }) => {
        const cursor = {
            lastPublishedAt: publishedAt,
            '@type': 'GetCreatorPostsPublishedAtCursor',
        }
        const { posts, pagination } = await dataSources.postAPI.getPosts(
            userId,
            {
                first,
                after: Buffer.from(JSON.stringify(cursor)).toString('base64'),
            },
            { by: PostSortFields.PUBLISHED_AT, order: direction },
            {}
        )
        return {
            nodes: posts,
            pageInfo: pagination,
        }
    },
}

export const postAssetResolvers: PostAssetResolvers = {
    __resolveType: ({ assetType }) => {
        switch (assetType) {
            case 'document':
                return 'PostDocumentAsset'
            case 'gjirafa':
                return 'PostGjirafaAsset'
            case 'gjirafa-livestream':
                return 'PostGjirafaLivestreamAsset'
            case 'image':
                return 'PostImageAsset'
            case 'youtube':
                return 'PostYoutubeAsset'
            case 'empty':
                return 'PostEmptyAsset'
        }
    },
}

export const postGjirafaAssetResolvers: PostGjirafaAssetResolvers = {
    timestamp: async ({ hasVideo, audioStreamUrl, audioStaticUrl, videoStreamUrl }, _, { dataSources, user }) => {
        if (!user) {
            return null
        }

        const key = hasVideo ? videoStreamUrl : (audioStreamUrl ?? audioStaticUrl)
        if (!key) {
            return null
        }

        const mediaStore = await dataSources.userMediaStoreAPI.getMediaStore(user.id)
        return mediaStore[key]
    },

    // eslint-disable-next-line @typescript-eslint/no-deprecated
    gjirafaId: ({ id }) => {
        return id
    },
}

export const postGjirafaLivestreamAssetResolvers: PostGjirafaLivestreamAssetResolvers = {
    // eslint-disable-next-line @typescript-eslint/no-deprecated
    gjirafaId: ({ id }) => {
        return id
    },

    // eslint-disable-next-line @typescript-eslint/no-deprecated
    startDateUTC: ({ id }, _, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        return dataSources.mediaAPI.getStartDateUTC(id.trim())
    },
}
