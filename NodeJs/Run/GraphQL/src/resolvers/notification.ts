import {
    CommentNotificationResolvers,
    MessageNotificationResolvers,
    NotificationResolvers,
    PostNotificationResolvers,
    SubscriberNotificationResolvers,
    SubscriptionNotificationResolvers,
} from '../generated/resolvers-types'
import { NotificationModel, NotificationType } from '../models/notification'
import { DataSourceContext } from '../context'
import { deletedUser, unauthorizedError } from './utils'
import { match } from 'ts-pattern'
import { StorageEntityType } from '../generated/api'
import { GraphQLError } from 'graphql/error'

export const notificationResolvers: NotificationResolvers = {
    __resolveType: (notification) => {
        return match(notification)
            .returnType<
                | 'GenericNotification'
                | 'PostNotification'
                | 'SubscriberNotification'
                | 'SubscriptionNotification'
                | 'CommentNotification'
                | 'MessageNotification'
            >()
            .with({ type: NotificationType.NEW_POST }, () => 'PostNotification')
            .with({ type: NotificationType.NEW_LIVESTREAM }, () => 'PostNotification')
            .with({ type: NotificationType.NEW_SUBSCRIPTION }, () => 'SubscriberNotification')
            .with({ type: NotificationType.SUBSCRIBE_REQUEST_ACCEPTED }, subscriptionNotification)
            .with({ type: NotificationType.CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS }, subscriptionNotification)
            .with({ type: NotificationType.CANCELLED_SUBSCRIPTION_ENDED }, subscriptionNotification)
            .with({ type: NotificationType.CANCELLED_SUBSCRIPTION_REFUSED }, subscriptionNotification)
            .with({ type: NotificationType.CANCELLED_SUBSCRIPTION_REFUNDED }, subscriptionNotification)
            .with({ type: NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR }, subscriptionNotification)
            .with({ type: NotificationType.CANCELLED_SUBSCRIPTION_OTHER }, subscriptionNotification)
            .with(
                {
                    type: NotificationType.PAYMENT_FAILED,
                    objectType: StorageEntityType.USER,
                },
                subscriptionNotification
            )
            .with(
                {
                    type: NotificationType.PAYMENT_INSUFFICIENT_FUNDS,
                    objectType: StorageEntityType.USER,
                },
                subscriptionNotification
            )
            .with(
                {
                    type: NotificationType.PAYMENT_CARD_DECLINED,
                    objectType: StorageEntityType.USER,
                },
                subscriptionNotification
            )
            .with({ type: NotificationType.NEW_COMMENT }, commentNotification)
            .with({ type: NotificationType.NEW_REPLY }, commentNotification)
            .with({ type: NotificationType.NEW_REPLY_TO_REPLY }, commentNotification)
            .with({ type: NotificationType.PAID_POST }, () => 'MessageNotification')
            .otherwise(() => 'GenericNotification')
    },

    targetId: ({ objectId }) => {
        return objectId ?? null
    },
}

const subscriptionNotification = (): 'SubscriptionNotification' => 'SubscriptionNotification'
const commentNotification = (): 'CommentNotification' => 'CommentNotification'

export const postNotificationResolvers: PostNotificationResolvers = {
    post: async ({ objectId }, _, { dataSources }) => {
        if (!objectId) {
            return null
        }

        try {
            return await dataSources.postAPI.getPost(objectId)
        } catch (_e) {
            return null
        }
    },

    creator: async (parent, _, context) => {
        return fetchLastActor(parent, context)
    },

    postId: ({ objectId, id }) => {
        if (!objectId) {
            throw new Error(`Expected post id, but was null or undefined in post notification ${id}`)
        }
        return objectId
    },
}

export const subscriberNotificationResolvers: SubscriberNotificationResolvers = {
    lastSubscriber: async (parent, _, context) => {
        return fetchLastActor(parent, context)
    },
}

export const subscriptionNotificationResolvers: SubscriptionNotificationResolvers = {
    creator: async (parent, _, context) => {
        switch (parent.type) {
            case NotificationType.PAYMENT_FAILED:
            case NotificationType.PAYMENT_CARD_DECLINED:
            case NotificationType.PAYMENT_INSUFFICIENT_FUNDS:
                return fetchObjectIdAsUser(parent, context)
            default:
                return fetchLastActor(parent, context)
        }
    },
}

export const commentNotificationResolvers: CommentNotificationResolvers = {
    commenter: async (parent, _, context) => {
        return fetchLastActor(parent, context)
    },

    postId: (parent, _, { user }) => {
        if (!user) {
            throw unauthorizedError()
        }
        return getPostId(parent, user.id)
    },

    commentId: ({ objectId, id }) => {
        if (!objectId) {
            throw new Error(`Expected comment id, but was null or undefined comment notification ${id}`)
        }
        return objectId
    },

    post: (parent, _, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }
        return dataSources.postAPI.getPost(getPostId(parent, user.id))
    },
}

function getPostId({ objectId, id, type }: NotificationModel, userId: string) {
    if (!objectId) {
        throw new GraphQLError(`Notification ${id} of type ${type} is missing objectId`)
    }

    // Find the first hyphen after the userId
    const nextHyphenIndex = objectId.indexOf('-', userId.length)

    // Extract the postId from the end of the userId to the first hyphen after
    return objectId.substring(0, nextHyphenIndex)
}

const messageThreadIdRegex = /^(\d+-\d+)-(\d+-\w+)$/
export const messageNotificationResolvers: MessageNotificationResolvers = {
    user: async (parent, _, context) => {
        return fetchLastActor(parent, context)
    },

    message: async ({ objectId }, _, { dataSources }) => {
        if (!objectId) {
            return null
        }
        return await dataSources.messageThreadAPI.getMessage(objectId)
    },

    messageThreadId: ({ objectId, id, type }) => {
        if (!objectId) {
            throw new GraphQLError(`Notification ${id} of type ${type} is missing objectId`)
        }

        const match = messageThreadIdRegex.exec(objectId)
        if (match) {
            return match[1]
        } else {
            throw new GraphQLError(`Notification ${id} of type ${type} has invalid comment id ${objectId}`)
        }
    },

    messageId: ({ objectId, id }) => {
        if (!objectId) {
            throw new Error(`Expected comment id, but was null or undefined message notification ${id}`)
        }
        return objectId
    },
}

async function fetchObjectIdAsUser({ objectId }: NotificationModel, { dataSources }: DataSourceContext) {
    if (!objectId) {
        return null
    }
    try {
        return await dataSources.userAPI.getUser(objectId)
    } catch (_e) {
        return deletedUser(objectId)
    }
}

async function fetchLastActor({ lastActor, lastActorId }: NotificationModel, { dataSources }: DataSourceContext) {
    if (lastActor) {
        return lastActor
    }
    try {
        return await dataSources.userAPI.getUser(lastActorId)
    } catch (_e) {
        return deletedUser(lastActorId)
    }
}
