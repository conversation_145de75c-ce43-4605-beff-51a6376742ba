/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Currency } from './Currency'
import type { StripeRequirements } from './StripeRequirements'

export type Creator = {
    tierId: string;
    stripeAccountId?: string | null;
    stripeAccountLegacyIds: Array<any>;
    stripeAccountActive: boolean;
    stripeAccountOnboarded: boolean;
    stripeRequirements?: StripeRequirements;
    suspended: boolean;
    currency?: Currency;
    emailPublic?: string | null;
    emailInvoice?: string | null;
    active: boolean;
    verified: boolean;
};

