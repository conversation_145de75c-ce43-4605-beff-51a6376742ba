import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { SubscribeRequestAPI } from '../../src/datasources/SubscribeRequestAPI'
import { PaginationModel } from '../../src/models/pagination'
import {
    PagedSubscribeRequestResponse,
    SubscribeRequestResponse,
    UpdateSubscribeRequestType,
} from '../../src/generated/api'
import { SubscribeRequestModel } from '../../src/models/subscription'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: SubscribeRequestAPI', () => {
    describe('method: createSubscribeRequest', () => {
        test('should make a call to create a subscribe request', async () => {
            // given
            const userId = 'user-id'
            const creatorId = 'creator-id'
            const underTest = new SubscribeRequestAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/subscribe-requests`, {
                    creatorId,
                })
                .reply(201)

            // when
            await underTest.createSubscribeRequest(creatorId)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: updateSubscribeRequest', () => {
        test('should make a call to update a subscribe request', async () => {
            // given
            const userId = 'user-id'
            const subscribeRequestId = 123
            const underTest = new SubscribeRequestAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v1/subscribe-requests/${subscribeRequestId}`, {
                    type: UpdateSubscribeRequestType.ACCEPT,
                })
                .reply(204)

            // when
            await underTest.updateSubscribeRequest(subscribeRequestId, UpdateSubscribeRequestType.ACCEPT)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getSubscribeRequest', () => {
        test('should make a call to fetch a subscribe request and return mapped data when found', async () => {
            // given
            const userId = 'user-id'
            const creatorId = 'creator-id'
            const subscribeRequestId = 123
            const underTest = new SubscribeRequestAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/subscribe-requests/${creatorId}`)
                .reply(200, {
                    data: subscribeRequestResponse(subscribeRequestId),
                })

            // when
            const result = await underTest.getSubscribeRequest(creatorId)

            // then
            expect(result).toEqual<SubscribeRequestModel>({
                id: subscribeRequestId,
                userId: 'user-id',
                creatorId: 'creator-id',
                createdAt: '2023-09-17T00:00:00Z',
                acceptedAt: '2023-09-18T00:00:00Z',
                declinedAt: null,
                deletedAt: null,
            })
            expect(scope.isDone()).toBeTruthy()
        })

        test('should return null when no subscribe request is found', async () => {
            // given
            const userId = 'user-id'
            const creatorId = 'creator-id'
            const underTest = new SubscribeRequestAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/subscribe-requests/${creatorId}`)
                .reply(200, { data: null })

            // when
            const result = await underTest.getSubscribeRequest(creatorId)

            // then
            expect(result).toBeNull()
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getSubscribeRequests', () => {
        test("should make a call to fetch user's subscribe requests", async () => {
            // given
            const userId = 'user-id'
            const subscribeRequestId = 123
            const underTest = new SubscribeRequestAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/subscribe-requests`)
                .query({
                    pageSize: 4,
                    afterCursor: 'after-cursor',
                })
                .reply(200, pagedSubscribeRequestResponse(subscribeRequestId))

            // when
            const { subscribeRequests, pagination } = await underTest.getSubscribeRequests({
                first: 4,
                after: 'after-cursor',
            })

            // then
            expect(subscribeRequests).toEqual<SubscribeRequestModel[]>([
                {
                    id: subscribeRequestId,
                    userId: 'user-id',
                    creatorId: 'creator-id',
                    createdAt: '2023-09-17T00:00:00Z',
                    acceptedAt: '2023-09-18T00:00:00Z',
                    declinedAt: null,
                    deletedAt: null,
                },
            ])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJmaXJzdElkIjoiaHVuZ2hvYW5nemZndm1kZW0tMTcwNjY4MjIxMCJ9',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })
})

function subscribeRequestResponse(subscribeRequestId: number): SubscribeRequestResponse {
    return {
        id: subscribeRequestId,
        userId: 'user-id',
        creatorId: 'creator-id',
        createdAt: '2023-09-17T00:00:00Z',
        acceptedAt: '2023-09-18T00:00:00Z',
        declinedAt: null,
        deletedAt: null,
    }
}

function pagedSubscribeRequestResponse(subscribeRequestId: number): PagedSubscribeRequestResponse {
    return {
        hasNext: false,
        afterCursor: 'eyJmaXJzdElkIjoiaHVuZ2hvYW5nemZndm1kZW0tMTcwNjY4MjIxMCJ9',
        content: [subscribeRequestResponse(subscribeRequestId)],
    }
}
