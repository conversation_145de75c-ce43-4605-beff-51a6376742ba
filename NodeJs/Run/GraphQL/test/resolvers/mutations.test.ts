import gql from 'graphql-tag'
import {
    category,
    comment,
    fullSubscription,
    notification,
    post,
    savedPost,
    testApolloServer,
    testContext,
    userDetails,
} from './test-utils'
import assert from 'assert'
import { Subscriber, SubscriberStatus, SubscriberType } from '../../src/generated/api'
import { SubscriptionStatus, SubscriptionType } from '../../src/generated/resolvers-types'
import { GraphQLResponse } from '@apollo/server'

test('mutation: assetTimestampUpdate', async () => {
    const mutation = gql`
        mutation AssetTimestampUpdate {
            assetTimestampUpdate(assetId: "vjsntmow", timestamp: 332.9, postId: "post-id") {
                success
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.userMediaStoreAPI.updateAssetTimestamp = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.userMediaStoreAPI.updateAssetTimestamp).toHaveBeenCalledWith(
        'vjsntmow',
        332.9,
        'post-id'
    )
})

test('mutation: categoryCreate', async () => {
    const mutation = gql`
        mutation CategoryCreate {
            categoryCreate(input: { name: "new category", index: 1 }) {
                category {
                    id
                    name
                    slug
                }
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    const categoryModel = category({ id: 'new' })
    const categoryModels = [category({ id: 'old-1' }), category({ id: 'old-2' })]
    context.dataSources.categoriesAPI.postCategory = jest.fn(async () => categoryModel)
    context.dataSources.categoriesAPI.getCategories = jest.fn(async () => categoryModels)
    context.dataSources.categoriesAPI.postCategoriesOrder = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.categoriesAPI.postCategory).toHaveBeenCalledWith({
        userId: 'user-id',
        categoryName: 'new category',
    })
    expect(context.dataSources.categoriesAPI.postCategoriesOrder).toHaveBeenCalledWith({
        userId: 'user-id',
        categoryIds: ['old-1', 'new', 'old-2'],
    })
})

test('mutation: categoryDelete', async () => {
    const mutation = gql`
        mutation CategoryCreate {
            categoryDelete(categoryId: "category-id") {
                success
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.categoriesAPI.deleteCategory = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.categoriesAPI.deleteCategory).toHaveBeenCalledWith({
        userId: 'user-id',
        categoryId: 'category-id',
    })
})

test('mutation: categoryUpdate', async () => {
    const mutation = gql`
        mutation CategoryUpdate {
            categoryUpdate(input: { name: "new category name" }, id: "category-id") {
                category {
                    id
                    name
                    slug
                }
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.categoriesAPI.patchCategory = jest.fn(async () => category())

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.categoriesAPI.patchCategory).toHaveBeenCalledWith({
        userId: 'user-id',
        categoryName: 'new category name',
        categoryId: 'category-id',
    })
})

test('mutation: categoryUpdate', async () => {
    const mutation = gql`
        mutation CategoriesOrder {
            categoriesOrder(input: { categoryIds: ["one", "two"] }) {
                success
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.categoriesAPI.postCategoriesOrder = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.categoriesAPI.postCategoriesOrder).toHaveBeenCalledWith({
        userId: 'user-id',
        categoryIds: ['one', 'two'],
    })
})

test('mutation: notificationUpdate', async () => {
    const mutation = gql`
        mutation UpdateNotification {
            notificationUpdate(
                id: "notification-1699259420"
                input: { checkedAt: "2023-10-06T06:33:10.926Z", seenAt: "2022-09-06T06:33:10.926Z" }
            ) {
                success
            }
        }
    `
    const context = testContext()
    context.dataSources.notificationAPI.updateNotification = jest.fn(async (id) => notification({ id }))

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.notificationAPI.updateNotification).toHaveBeenCalledWith(
        'notification-1699259420',
        new Date('2023-10-06T06:33:10.926Z'),
        new Date('2022-09-06T06:33:10.926Z')
    )
})

test('mutation: postAddToLibrary', async () => {
    const mutation = gql`
        mutation {
            postAddToLibrary(postId: "vojtechknyttlnjirpwodtttilcziivdlbpeja") {
                savedPost {
                    savedAt
                    post {
                        id
                    }
                }
            }
        }
    `
    const context = testContext()
    context.dataSources.libraryAPI.addPostToLibrary = jest.fn(async (id) => savedPost(id))

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.libraryAPI.addPostToLibrary).toHaveBeenCalledWith(
        'vojtechknyttlnjirpwodtttilcziivdlbpeja'
    )
})

test('mutation: postRemoveFromLibrary', async () => {
    const mutation = gql`
        mutation {
            postRemoveFromLibrary(postId: "hunghoangzfgvmdem-vojtechknyttlnjirpwodtttilcziivdlbpeja") {
                success
            }
        }
    `
    const context = testContext()
    context.dataSources.libraryAPI.removePostFromLibrary = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.libraryAPI.removePostFromLibrary).toHaveBeenCalledWith(
        'hunghoangzfgvmdem-vojtechknyttlnjirpwodtttilcziivdlbpeja'
    )
})

test('mutation: commentCreate', async () => {
    const mutation = gql`
        mutation {
            commentCreate(
                parentId: "parent-id"
                siblingId: "sibling-id"
                attributes: {
                    text: "text"
                    textHtml: "text-html"
                    assets: [{ image: { height: 512, width: 513, url: "image-url" } }]
                }
            ) {
                comment {
                    id
                }
            }
        }
    `
    const context = testContext()
    context.dataSources.postAPI.createComment = jest.fn(async () => comment())

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.createComment).toHaveBeenCalledWith(
        'parent-id',
        {
            text: 'text',
            textHtml: 'text-html',
            assets: [{ image: { height: 512, width: 513, url: 'image-url' } }],
        },
        'sibling-id'
    )
})

test('mutation: commentUpdate', async () => {
    const mutation = gql`
        mutation {
            commentUpdate(
                commentId: "comment-id"
                attributes: {
                    text: "text"
                    textHtml: "text-html"
                    assets: [{ image: { height: 512, width: 513, url: "image-url" } }]
                }
            ) {
                comment {
                    id
                }
            }
        }
    `
    const context = testContext()
    context.dataSources.postAPI.updateComment = jest.fn(async () => comment())

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.updateComment).toHaveBeenCalledWith('comment-id', {
        text: 'text',
        textHtml: 'text-html',
        assets: [{ image: { height: 512, width: 513, url: 'image-url' } }],
    })
})

test('mutation: commentDelete', async () => {
    const mutation = gql`
        mutation {
            commentDelete(commentId: "comment-id") {
                success
            }
        }
    `
    const context = testContext()
    context.dataSources.postAPI.deleteComment = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.deleteComment).toHaveBeenCalledWith('comment-id')
})

test('mutation: postDelete', async () => {
    const mutation = gql`
        mutation {
            postDelete(postId: "post-id") {
                success
            }
        }
    `
    const context = testContext()
    context.dataSources.postAPI.deletePost = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.deletePost).toHaveBeenCalledWith('post-id')
})

test('mutation: postCreate', async () => {
    const mutation = gql`
        mutation {
            postCreate(
                attributes: {
                    text: "text"
                    textHtml: "text-html"
                    assets: [{ image: { height: 512, width: 513, url: "image-url" } }]
                    categories: ["sports"]
                    publishedAt: "2023-11-06T06:33:10.926Z"
                }
            ) {
                post {
                    id
                }
            }
        }
    `
    const context = testContext()
    context.dataSources.postAPI.createPost = jest.fn(async () => post())

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.createPost).toHaveBeenCalledWith({
        text: 'text',
        textHtml: 'text-html',
        assets: [{ image: { height: 512, width: 513, url: 'image-url' } }],
        categories: ['sports'],
        publishedAt: new Date('2023-11-06T06:33:10.926Z'),
    })
})

describe('mutation: postUpdate', () => {
    test('full update', async () => {
        const mutation = gql`
            mutation {
                postUpdate(
                    postId: "post-id"
                    attributes: {
                        text: "text"
                        textHtml: "text-html"
                        assets: [{ image: { height: 512, width: 513, url: "image-url" } }]
                        categories: ["sports"]
                        publishedAt: "2023-11-06T06:33:10.926Z"
                        pinnedAt: "2023-11-07T06:33:10.926Z"
                        isExcludedFromRss: true
                        isAgeRestricted: true
                        isSponsored: true
                        textDelta: "text-delta"
                    }
                ) {
                    post {
                        id
                    }
                }
            }
        `
        const context = testContext()
        context.dataSources.postAPI.updatePost = jest.fn(async () => post())
        context.dataSources.postAPI.getPost = jest.fn(async () => post({ text: 'orig', textHtml: 'orig-html' }))

        const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.postAPI.updatePost).toHaveBeenCalledWith('post-id', {
            text: 'text',
            textHtml: 'text-html',
            assets: [{ image: { height: 512, width: 513, url: 'image-url' } }],
            categories: ['sports'],
            publishedAt: new Date('2023-11-06T06:33:10.926Z'),
            pinnedAt: new Date('2023-11-07T06:33:10.926Z'),
            isAgeRestricted: true,
            isSponsored: true,
            isExcludedFromRss: true,
            textDelta: 'text-delta',
        })
    })

    test('should only remove pin, other values should be used from the original', async () => {
        const mutation = gql`
            mutation {
                postUpdate(postId: "post-id", attributes: { pinnedAt: null }) {
                    post {
                        id
                    }
                }
            }
        `
        const context = testContext()
        context.dataSources.postAPI.updatePost = jest.fn(async () => post())
        const originalPost = post({
            text: 'orig',
            textHtml: 'orig-html',
            pinnedAt: '2025-02-26T19:21:45.441Z',
            publishedAt: '2025-03-26T19:21:45.441Z',
            textDelta: 'text-delta',
            categories: [{ id: 'category-id', name: 'category-name', slug: 'category-slug' }],
            isSponsored: false,
            isAgeRestricted: true,
            isExcludedFromRss: false,
            assets: [{ height: 512, width: 513, url: 'image-url', assetType: 'image' }],
        })
        context.dataSources.postAPI.getPost = jest.fn(async () => originalPost)

        const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.postAPI.updatePost).toHaveBeenCalledWith('post-id', {
            text: 'orig',
            textHtml: 'orig-html',
            pinnedAt: undefined,
            publishedAt: undefined,
            textDelta: 'text-delta',
            assets: [{ image: { height: 512, width: 513, url: 'image-url', assetType: 'image' } }],
            categories: ['category-id'],
            isAgeRestricted: true,
            isSponsored: false,
            isExcludedFromRss: false,
        })
    })
})

test('mutation: notificationMarkAllSeen', async () => {
    const mutation = gql`
        mutation {
            notificationMarkAllSeen {
                success
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.notificationAPI.markAllSeen = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.notificationAPI.markAllSeen).toHaveBeenCalledWith('user-id')
})

test('mutation: subscribeRequestCreate', async () => {
    const mutation = gql`
        mutation {
            subscribeRequestCreate(input: { creatorId: "creator-id" }) {
                success
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.subscribeRequestAPI.createSubscribeRequest = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.subscribeRequestAPI.createSubscribeRequest).toHaveBeenCalledWith('creator-id')
})

test('mutation: subscribeRequestAccept', async () => {
    const mutation = gql`
        mutation {
            subscribeRequestAccept(input: { id: "123" }) {
                success
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.subscribeRequestAPI.updateSubscribeRequest = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.subscribeRequestAPI.updateSubscribeRequest).toHaveBeenCalledWith(123, 'ACCEPT')
})

describe('mutation: viewerUpdate', () => {
    test('all inputs are assigned', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(
                    userDetails: {
                        name: "pepa"
                        bio: "ja jsem pepa"
                        path: "pepinov"
                        profileImage: { url: "image-url", height: 500, width: 400 }
                        isOfAge: true
                        emailInvoice: "<EMAIL>"
                        emailPublic: "<EMAIL>"
                    }
                ) {
                    userDetails {
                        id
                        path
                        name
                        bio
                    }
                    errors {
                        property
                        value
                        errorType
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: userDetails({ id: 'user-id' }) }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => userDetails({ id: 'user-id' }))

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: 'pepa',
            bio: 'ja jsem pepa',
            path: 'pepinov',
            profileImage: { url: 'image-url', height: 500, width: 400 },
            isOfAge: true,
            emailInvoice: '<EMAIL>',
            emailPublic: '<EMAIL>',
        })
    })

    test('only name is passed', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { name: "pepa" }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({ id: 'user-id' })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: 'pepa',
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            profileImage: {
                url: mockedUserDetails.image?.url,
                height: mockedUserDetails.image?.height,
                width: mockedUserDetails.image?.width,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: mockedUserDetails.emailInvoice,
        })
    })

    test('only bio is passed', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { bio: "pepa is my god" }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({ id: 'user-id' })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: 'pepa is my god',
            path: mockedUserDetails.path,
            profileImage: {
                url: mockedUserDetails.image?.url,
                height: mockedUserDetails.image?.height,
                width: mockedUserDetails.image?.width,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: mockedUserDetails.emailInvoice,
        })
    })

    test('only path is passed', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { path: "pepapath" }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({
            id: 'user-id',
            profilePicture: {
                url: 'profile-url',
                width: 300,
                height: 500,
                hidden: true,
            },
        })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: 'pepapath',
            profileImage: {
                url: 'profile-url',
                width: 300,
                height: 500,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: mockedUserDetails.emailInvoice,
        })
    })

    test('only isOfAge is passed', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { isOfAge: false }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({ id: 'user-id', isOfAge: true })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            profileImage: {
                height: 69,
                url: 'image-id',
                width: 420,
            },
            isOfAge: false,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: mockedUserDetails.emailInvoice,
        })
    })

    test('only profile picture is passed', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { profileImage: { url: "url-changed", width: 100, height: 200 } }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({
            id: 'user-id',
            profilePicture: {
                url: 'original-url',
                width: 212,
                height: 432,
                hidden: true,
            },
        })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            profileImage: {
                url: 'url-changed',
                width: 100,
                height: 200,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: mockedUserDetails.emailInvoice,
        })
    })

    test('profile picture is deleted - set to null', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { profileImage: null }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({
            id: 'user-id',
            profilePicture: {
                url: 'original-url',
                width: 100,
                height: 200,
                hidden: false,
            },
        })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: mockedUserDetails.emailInvoice,
        })
    })

    test('only emailPublic is passed', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { emailPublic: "<EMAIL>" }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({ id: 'user-id' })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            profileImage: {
                url: mockedUserDetails.image?.url,
                height: mockedUserDetails.image?.height,
                width: mockedUserDetails.image?.width,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: '<EMAIL>',
            emailInvoice: mockedUserDetails.emailInvoice,
        })
    })

    test('only emailInvoice is passed', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { emailInvoice: "<EMAIL>" }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({ id: 'user-id' })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            profileImage: {
                url: mockedUserDetails.image?.url,
                height: mockedUserDetails.image?.height,
                width: mockedUserDetails.image?.width,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: '<EMAIL>',
        })
    })

    test('emailPublic is set to null', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { emailPublic: null }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({ id: 'user-id' })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            profileImage: {
                url: mockedUserDetails.image?.url,
                height: mockedUserDetails.image?.height,
                width: mockedUserDetails.image?.width,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailInvoice: mockedUserDetails.emailInvoice,
            emailPublic: undefined,
        })
    })

    test('emailInvoice is set to null', async () => {
        const mutation = gql`
            mutation {
                viewerUpdate(userDetails: { emailInvoice: null }) {
                    userDetails {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const mockedUserDetails = userDetails({ id: 'user-id' })
        context.dataSources.userAPI.updateUsersDetails = jest.fn(async () => {
            return { userDetails: mockedUserDetails }
        })
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => mockedUserDetails)

        assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

        expect(context.dataSources.userAPI.updateUsersDetails).toHaveBeenCalledWith('user-id', {
            name: mockedUserDetails.name,
            bio: mockedUserDetails.bio,
            path: mockedUserDetails.path,
            profileImage: {
                url: mockedUserDetails.image?.url,
                height: mockedUserDetails.image?.height,
                width: mockedUserDetails.image?.width,
            },
            isOfAge: mockedUserDetails.isOfAge,
            emailPublic: mockedUserDetails.emailPublic,
            emailInvoice: undefined,
        })
    })
})

describe('mutation: subscriptionDelete', () => {
    test('subscription is past_due', async () => {
        const mutation = gql`
            mutation {
                subscriptionCancel(creatorId: "creator-id") {
                    success
                    subscription {
                        id
                        cancelAtPeriodEnd
                        couponAppliedForMonths
                        expires
                        status
                        type
                        subscribedAt
                        tier {
                            id
                            currency
                            priceCents
                        }
                        creator {
                            id
                        }
                        subscriber {
                            id
                        }
                    }
                }
            }
        `

        const context = testContext({ userId: 'user-id' })
        context.dataSources.subscriptionAPI.getSubscription = jest.fn(async () =>
            fullSubscription({
                id: 'subscription-id',
                subscribedAt: '2023-11-06T06:33:10.926Z',
                tierId: 'EUR03',
                type: SubscriptionType.STRIPE,
                couponAppliedForMonths: 10,
                // this changes the behaviour of the test
                status: SubscriptionStatus.PAST_DUE,
            })
        )
        const subscriber: Subscriber = {
            id: 'user-id|creator-id',
            userId: 'user-id',
            creatorId: 'creator-id',
            status: SubscriberStatus.CANCELLED,
            cancelAtPeriodEnd: true,
            subscribed: '2023-10-06T06:33:10.926Z',
            subscriberType: SubscriberType.STRIPE,
            refunded: false,
            refused: false,
            tierId: 'EUR05',
            expires: '2023-10-10T06:33:10.926Z',
        }
        context.dataSources.subscriptionAPI.deleteSubscription = jest.fn(async () => subscriber)
        context.dataSources.subscriptionAPI.patchSubscription = jest.fn()

        const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscription).toHaveBeenCalledWith('user-id', 'creator-id')
        expect(context.dataSources.subscriptionAPI.deleteSubscription).toHaveBeenCalledWith(
            'user-id',
            'creator-id',
            'user'
        )
        expect(context.dataSources.subscriptionAPI.patchSubscription).not.toHaveBeenCalled()
    })

    test('subscription is active', async () => {
        const mutation = gql`
            mutation {
                subscriptionCancel(creatorId: "creator-id") {
                    success
                    subscription {
                        id
                        cancelAtPeriodEnd
                        couponAppliedForMonths
                        expires
                        status
                        type
                        subscribedAt
                        tier {
                            id
                            currency
                            priceCents
                        }
                        creator {
                            id
                        }
                        subscriber {
                            id
                        }
                    }
                }
            }
        `

        const context = testContext({ userId: 'user-id' })
        context.dataSources.subscriptionAPI.getSubscription = jest.fn(async () =>
            fullSubscription({
                id: 'subscription-id',
                subscribedAt: '2023-11-06T06:33:10.926Z',
                tierId: 'EUR03',
                type: SubscriptionType.STRIPE,
                couponAppliedForMonths: 10,
                // this changes the behaviour of the test
                status: SubscriptionStatus.ACTIVE,
            })
        )
        const subscriber: Subscriber = {
            id: 'user-id|creator-id',
            userId: 'user-id',
            creatorId: 'creator-id',
            status: SubscriberStatus.CANCELLED,
            cancelAtPeriodEnd: true,
            subscribed: '2023-10-06T06:33:10.926Z',
            subscriberType: SubscriberType.STRIPE,
            refunded: false,
            refused: false,
            tierId: 'EUR05',
            expires: '2023-10-10T06:33:10.926Z',
        }
        context.dataSources.subscriptionAPI.deleteSubscription = jest.fn()
        context.dataSources.subscriptionAPI.patchSubscription = jest.fn(async () => subscriber)

        const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscription).toHaveBeenCalledWith('user-id', 'creator-id')
        expect(context.dataSources.subscriptionAPI.patchSubscription).toHaveBeenCalledWith('user-id', 'creator-id', {
            cancelAtPeriodEnd: true,
        })
        expect(context.dataSources.subscriptionAPI.deleteSubscription).not.toHaveBeenCalled()
    })
})

describe('mutation: subscriptionRenew ', () => {
    test('should call backend to renew the subscription', async () => {
        const mutation = gql`
            mutation {
                subscriptionRenew(creatorId: "creator-id") {
                    success
                    subscription {
                        id
                        cancelAtPeriodEnd
                        couponAppliedForMonths
                        expires
                        status
                        type
                        subscribedAt
                        tier {
                            id
                            currency
                            priceCents
                        }
                        creator {
                            id
                        }
                        subscriber {
                            id
                        }
                    }
                }
            }
        `

        const context = testContext({ userId: 'user-id' })
        context.dataSources.subscriptionAPI.getSubscription = jest.fn(async () =>
            fullSubscription({
                id: 'subscription-id',
                subscribedAt: '2023-11-06T06:33:10.926Z',
                tierId: 'EUR03',
                type: SubscriptionType.STRIPE,
                couponAppliedForMonths: 10,
                cancelAtPeriodAt: true,
                status: SubscriptionStatus.ACTIVE,
            })
        )
        const subscriber: Subscriber = {
            id: 'user-id|creator-id',
            userId: 'user-id',
            creatorId: 'creator-id',
            status: SubscriberStatus.ACTIVE,
            cancelAtPeriodEnd: false,
            subscribed: '2023-10-06T06:33:10.926Z',
            subscriberType: SubscriberType.STRIPE,
            refunded: false,
            refused: false,
            tierId: 'EUR05',
            expires: '2023-10-10T06:33:10.926Z',
        }

        context.dataSources.subscriptionAPI.patchSubscription = jest.fn(async () => subscriber)

        const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscription).toHaveBeenCalledWith('user-id', 'creator-id')
        expect(context.dataSources.subscriptionAPI.patchSubscription).toHaveBeenCalledWith('user-id', 'creator-id', {
            cancelAtPeriodEnd: false,
        })
    })

    test('subscription is active, no call to backend should be done to patch', async () => {
        const mutation = gql`
            mutation {
                subscriptionRenew(creatorId: "creator-id") {
                    success
                    subscription {
                        id
                        cancelAtPeriodEnd
                        couponAppliedForMonths
                        expires
                        status
                        type
                        subscribedAt
                        tier {
                            id
                            currency
                            priceCents
                        }
                        creator {
                            id
                        }
                        subscriber {
                            id
                        }
                    }
                }
            }
        `

        const context = testContext({ userId: 'user-id' })
        context.dataSources.subscriptionAPI.getSubscription = jest.fn(async () =>
            fullSubscription({
                id: 'subscription-id',
                subscribedAt: '2023-11-06T06:33:10.926Z',
                tierId: 'EUR03',
                type: SubscriptionType.STRIPE,
                couponAppliedForMonths: 10,
                cancelAtPeriodAt: false,
                status: SubscriptionStatus.ACTIVE,
            })
        )

        context.dataSources.subscriptionAPI.patchSubscription = jest.fn()
        const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscription).toHaveBeenCalledWith('user-id', 'creator-id')
        expect(context.dataSources.subscriptionAPI.patchSubscription).not.toHaveBeenCalledWith()
    })
})

test('mutation: subscriberDelete', async () => {
    const mutation = gql`
        mutation {
            subscriberDelete(subscriberId: "subscriber-id") {
                success
                subscription {
                    id
                    cancelAtPeriodEnd
                    couponAppliedForMonths
                    expires
                    status
                    type
                    subscribedAt
                    tier {
                        id
                        currency
                        priceCents
                    }
                    creator {
                        id
                    }
                    subscriber {
                        id
                    }
                }
            }
        }
    `

    const context = testContext({ userId: 'creator-id', role: 'moderator' })
    context.dataSources.subscriptionAPI.getSubscriber = jest.fn(async () =>
        fullSubscription({
            id: 'subscription-id',
            subscribedAt: '2023-11-06T06:33:10.926Z',
            tierId: 'EUR03',
            type: SubscriptionType.STRIPE,
            couponAppliedForMonths: 10,
            status: SubscriptionStatus.ACTIVE,
        })
    )
    const subscriber: Subscriber = {
        id: 'user-id|creator-id',
        userId: 'user-id',
        creatorId: 'creator-id',
        status: SubscriberStatus.CANCELLED,
        cancelAtPeriodEnd: true,
        subscribed: '2023-10-06T06:33:10.926Z',
        subscriberType: SubscriberType.STRIPE,
        refunded: false,
        refused: false,
        tierId: 'EUR05',
        expires: '2023-10-10T06:33:10.926Z',
    }
    context.dataSources.subscriptionAPI.deleteSubscription = jest.fn(async () => subscriber)
    context.dataSources.subscriptionAPI.patchSubscription = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.subscriptionAPI.getSubscriber).toHaveBeenCalledWith('creator-id', 'subscriber-id')
    expect(context.dataSources.subscriptionAPI.deleteSubscription).toHaveBeenCalledWith(
        'subscriber-id',
        'creator-id',
        'moderator'
    )
    expect(context.dataSources.subscriptionAPI.patchSubscription).not.toHaveBeenCalled()
})

test('mutation: rssFeedUrlGenerate', async () => {
    const mutation = gql`
        mutation {
            rssFeedUrlGenerate(creatorId: "cestmir") {
                rssFeedUrl {
                    url
                }
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.userAPI.rssFeedUrl = jest.fn(async () => ({ url: 'feed-url' }))

    assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

    expect(context.dataSources.userAPI.rssFeedUrl).toHaveBeenCalledWith('cestmir')
})

test('mutation: sessionRevoke', async () => {
    const mutation = gql`
        mutation {
            sessionRevoke {
                success
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    context.dataSources.sessionAPI.deleteSessions = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.sessionAPI.deleteSessions).toHaveBeenCalledWith()
})

test('mutation: sessionRevoke without authentication should fail', async () => {
    const mutation = gql`
        mutation {
            sessionRevoke {
                success
            }
        }
    `
    const context = testContext() // No userId provided
    context.dataSources.sessionAPI.deleteSessions = jest.fn()

    const response = await testApolloServer.executeOperation({ query: mutation }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeDefined()
    expect(response.body.singleResult.errors?.[0]?.message).toBe('Unauthorized')

    expect(context.dataSources.sessionAPI.deleteSessions).not.toHaveBeenCalled()
})

test('mutation: notificationSettingsUpdate', async () => {
    const mutation = gql`
        mutation {
            notificationSettingsUpdate(input: { termsChanged: true, emailNewDm: false }) {
                success
            }
        }
    `
    const previousNotificationSettings = {
        emailNewDm: true,
        emailNewPost: false,
        newsletter: false,
        termsChanged: false,
        pushNewPost: false,
        pushNewComment: false,
    }

    const context = testContext({ userId: 'user-id' })
    context.dataSources.notificationSettingsAPI.getNotificationSettings = jest.fn(
        async () => previousNotificationSettings
    )
    context.dataSources.notificationSettingsAPI.updateNotificationSettings = jest.fn()

    assertResponse(await testApolloServer.executeOperation({ query: mutation }, { contextValue: context }))

    expect(context.dataSources.notificationSettingsAPI.getNotificationSettings).toHaveBeenCalled()
    // termsChanged from false to true, emailNewDm from true to false, others defaulted to false
    expect(context.dataSources.notificationSettingsAPI.updateNotificationSettings).toHaveBeenCalledWith({
        emailNewDm: false,
        emailNewPost: false,
        newsletter: false,
        termsChanged: true,
        pushNewPost: false,
        pushNewComment: false,
    })
})

function assertResponse(response: GraphQLResponse) {
    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)
}
