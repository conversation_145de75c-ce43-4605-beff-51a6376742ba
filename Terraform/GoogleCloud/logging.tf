# As GCP logging is paid by amount of ingested logs, we want to log only those which are relevant
# and not infinitely repeating without meaningful value. This is a filter of such logs so we don't
# pay what we don't need to. See sink detail, where you can also see a preview of such logs:
# https://console.cloud.google.com/logs/router/sink/projects%2Fheroheroco%2Fsinks%2F_Default?project=heroheroco
resource "google_logging_project_sink" "default_logging_sink" {
  name                   = "_Default"
  project                = "heroheroco"
  destination            = "logging.googleapis.com/projects/heroheroco/locations/global/buckets/_Default"
  disabled               = false
  unique_writer_identity = true
  filter                 = <<EOT
    NOT LOG_ID("cloudaudit.googleapis.com/access_transparency")
    NOT LOG_ID("cloudaudit.googleapis.com/activity")
    NOT LOG_ID("cloudaudit.googleapis.com/system_event")
    NOT LOG_ID("externalaudit.googleapis.com/access_transparency")
    NOT LOG_ID("externalaudit.googleapis.com/activity")
    NOT LOG_ID("externalaudit.googleapis.com/system_event")
    NOT jsonPayload.@type="type.googleapis.com/google.devtools.clouderrorreporting.v1beta1.Insight"
    NOT jsonPayload."cos.googleapis.com/container_name":"gitlab-runner"
    NOT textPayload="Function execution started"
    NOT textPayload:"org.eclipse.jetty.util.log.StdErrLog"
    NOT textPayload:"Publishing to topic"
    NOT textPayload:"finished with status: 'ok'"
    NOT textPayload:"finished with status code: 200"
    NOT textPayload:"finished with status code: 403"
    NOT textPayload:"SLF4J:"
    NOT textPayload:"Unsupported syscall"
    NOT textPayload:"Started ServerConnector"
    NOT textPayload:"No setter/field"
    NOT textPayload:"com.google.cloud.firestore.encoding.PojoBeanMapper"
    NOT textPayload:"com.google.cloud.firestore.CustomClassMapper$BeanMapper deserialize"
    NOT textPayload:"Got more than one input Future failure. Logging failures after the first"
    NOT textPayload:"RuntimeException while executing runnable CallbackListener"
    NOT textPayload:"ERROR StatusLogger Unrecognized"
    NOT textPayload:"Sending aggregate usage beacon"
    NOT textPayload:"does not subscribe"
    NOT textPayload="Unauthorized."
    NOT (textPayload:"Subscription" AND textPayload:"not found.")
    NOT labels."compute.googleapis.com/resource_name":"gitlab-runner"
    NOT httpRequest.status = 200
    NOT httpRequest.status = 204
EOT
}

resource "google_logging_project_sink" "fatal_logs_devel" {
  name                   = "fatal-logs-devel"
  destination            = "pubsub.googleapis.com/projects/heroheroco/topics/devel-FatalLogs"
  filter                 = <<EOT
-resource.labels.service_name:"slack-post-message"
-resource.labels.service_name:"slack-fatal-notifier"
resource.labels.service_name:"devel" OR resource.labels.function_name:"devel"
severity="ALERT" OR httpRequest.status = 400 OR textPayload:"OutOfMemory" OR textPayload:"finished with status: 'timeout'"
  EOT
  unique_writer_identity = true
}

resource "google_logging_project_sink" "fatal_logs_prod" {
  name                   = "fatal-logs-prod"
  destination            = "pubsub.googleapis.com/projects/heroheroco/topics/prod-FatalLogs"
  filter                 = <<EOT
-resource.labels.service_name:"slack-post-message"
-resource.labels.service_name:"slack-fatal-notifier"
resource.labels.service_name:"prod" OR resource.labels.function_name:"prod" OR resource.labels.service_name:"staging" OR resource.labels.function_name:"staging"
severity="ALERT" OR textPayload:"OutOfMemory" OR textPayload:"finished with status: 'timeout'"
  EOT
  unique_writer_identity = true
}

// TODO log alert for JS functions, Kotlin function are logged above
