# https://docs.gitlab.com/ee/user/infrastructure/iac/mr_integration.html
include:
  - "/Terraform/BunnyCdn/.gitlab-ci.yml"
  - "/Terraform/Cloudflare/.gitlab-ci.yml"
  - "/Terraform/GoogleCloud/.gitlab-ci.yml"
  - "/Terraform/StripeProdEu/.gitlab-ci.yml"
  - "/Terraform/StripeProdUs/.gitlab-ci.yml"
  - "/Terraform/StripeTestEu/.gitlab-ci.yml"
  - "/Terraform/StripeTestUs/.gitlab-ci.yml"

.terraform-ver-init: &terraform-init
  - terraform version
  - terraform init --upgrade=True

.tf_rules:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never

variables:
  TF_PLAN_NAME: plan.tfplan
  TF_PLAN_JSON: plan.json

.terraform-plan:
  stage: terraform
  extends: .job-with-retries
  resource_group: ${TF_STATE_NAME}
  allow_failure: true
  needs: []
  before_script:
    - cd Terraform/$TF_NAME
    - *terraform-init
    - shopt -s expand_aliases
    - alias convert_report="jq -r '([.resource_changes[]?.change.actions?]|flatten)|{\"create\":(map(select(.==\"create\"))|length),\"update\":(map(select(.==\"update\"))|length),\"delete\":(map(select(.==\"delete\"))|length)}'"
  script:
    - terraform plan -out=$TF_PLAN_NAME -refresh=true
    - terraform show --json $TF_PLAN_NAME | convert_report > $TF_PLAN_JSON
  artifacts:
    reports:
      terraform: "**/${TF_PLAN_JSON}"
    paths:
      - "**/${TF_PLAN_JSON}"
      - "**/${TF_PLAN_NAME}"
  rules:
    - !reference [ .tf_rules, rules ]
    - changes:
        paths:
          - "Terraform/$TF_NAME/**/*"
        compare_to: 'main'
      when: always
    - when: manual

.terraform-apply:
  stage: terraform
  extends: .job-with-retries
  resource_group: ${TF_STATE_NAME}
  allow_failure: true
  before_script:
    - cd Terraform/$TF_NAME
    - *terraform-init
  rules:
    - !reference [ .tf_rules, rules ]
    - when: manual
  when: manual
  script:
    - terraform apply -auto-approve
