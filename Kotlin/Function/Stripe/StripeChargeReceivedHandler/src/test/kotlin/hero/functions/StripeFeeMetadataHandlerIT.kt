package hero.functions

import com.stripe.net.RequestOptions
import com.stripe.param.ChargeRetrieveParams
import com.stripe.param.ChargeUpdateParams
import hero.baseutils.SystemEnv
import hero.model.Currency
import hero.stripe.service.StripeClients
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.junit.jupiter.api.assertNull
import kotlin.test.assertEquals

class StripeFeeMetadataHandlerIT {
    // These tests would be hard to perform on manually created entities, so we perform it live in Stripe.
    // Following charge relates to subscription `sub_1RLi5pEnxBLonuEsow3X9O1P`
    // TODO this may need some tuning as Stripe will probably delete related `charge.invoice`. We may need
    //      to test against production. Let's see in 2025/08 if this test still passes.
    private val chargeId = "ch_3RY4p4EnxBLonuEs0xFbI60J"

    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val underTest: StripeFeeMetadataHandler = StripeFeeMetadataHandler(stripeClients)
    private val charges = stripeClients[Currency.USD].charges()

    @Test
    fun `metadata are propagated inside creators' transaction details`() {
        val expectedMeta = mapOf(
            "Fee Herohero 7.00%" to "\$0.63",
            "Fee Stripe \$0.30" to "\$0.30",
            "Fee Stripe 2.90%" to "\$0.26",
        )

        val charge = charges.retrieve(
            chargeId,
            ChargeRetrieveParams.builder().addAllExpand(listOf("invoice.subscription", "transfer")).build(),
        )

        val destination = RequestOptions.builder().setStripeAccount(charge.transferObject.destination).build()

        // clean metadata of given payment inside connected account
        // note there may be some other metadata, eg `payoutId`
        charges.update(
            charge.transferObject.destinationPayment,
            ChargeUpdateParams.builder().setMetadata(expectedMeta.keys.associateWith { "" }).build(),
            destination,
        )

        val paymentBefore = charges.retrieve(charge.transferObject.destinationPayment, destination)

        // checking that the payment is actually empty after clean up in previous step
        expectedMeta.keys.forEach {
            assertNull(paymentBefore.metadata[it])
        }

        // run the process
        underTest.storeApplicationFeesInHumanReadbleFormForCreator(charge)

        // and check the updated charge
        val paymentAfter = charges.retrieve(
            charge.transferObject.destinationPayment,
            destination,
        )

        expectedMeta.keys.forEach {
            assertNotNull(paymentAfter.metadata[it])
            assertEquals(expectedMeta[it], paymentAfter.metadata[it])
        }
    }

    @Test
    fun `metadata are propagated from subscriptions to relevant charges`() {
        val relevantFields = listOf("appFeeHerohero", "appFeeStripeDynamic", "appFeeStripeFixed")

        val charge = charges.retrieve(
            chargeId,
            ChargeRetrieveParams.builder().addAllExpand(listOf("invoice.subscription", "transfer")).build(),
        )

        // clean metadata of given charge (note there may be some other metadata)
        charge.update(
            ChargeUpdateParams.builder().setMetadata(relevantFields.associateWith { "" }).build(),
        )

        val chargeBefore = charges.retrieve(charge.id)

        // checking that the payment is actually empty after clean up in previous step
        relevantFields.forEach {
            assertNull(chargeBefore.metadata[it])
        }

        // run the process
        underTest.storeApplicationFeesWithinCharges(charge)

        // and check the updated charge
        val chargeAfter = charges.retrieve(charge.id)

        relevantFields.forEach {
            assertNotNull(chargeAfter.metadata[it])
            assertEquals(charge.invoiceObject.subscriptionObject.metadata[it], chargeAfter.metadata[it])
        }
    }
}
