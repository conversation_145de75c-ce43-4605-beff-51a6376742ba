package hero.functions

import com.stripe.param.TransferCreateParams
import com.stripe.param.TransferListParams
import com.stripe.param.TransferReversalCollectionCreateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.model.CouponTarget
import hero.model.PaymentType
import hero.model.Subscriber
import hero.model.Tier
import hero.model.topics.CouponApplied
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.VatMapping
import hero.stripe.service.VatMappingProvider
import hero.stripe.service.computeFee
import java.math.BigDecimal
import java.time.Instant

@Suppress("unused")
class StripeCouponAppliedHandler(
    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs),
    private val stripeService: StripeService = StripeService(
        clients = stripeClients,
        pubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    ),
    private val countryToVatMapping: VatMapping = VatMappingProvider(
        systemEnv("FLEXIBEE_PASSWORD"),
    ).countryToVatMapping(),
) : PubSubSubscriber<CouponApplied>() {
    /**
     * When coupon is applied, we need to reverse the transfer of the coupon value from Herohero coupon account
     * back to the main Herohero account and then transfer it to the subscription's creator.
     */
    override fun consume(payload: CouponApplied) {
        // find the subscription and assert its quality
        val subscription = stripeClients[payload.currency].subscriptions().retrieve(payload.subscriptionId)
        val coupon = subscription.discount.coupon
        // creator's coupons & apple ones are transferred immediately, no action needed
        if (coupon.metadata["couponType"] != CouponTarget.HEROHERO.name) {
            return
        }
        error("Before using Herohero coupons, we have to handle correctly EU vs US keys below.")
        val creatorVatId = subscription.metadata["creatorVatId"]
        val userId = subscription.metadata["userId"]
            ?: error("Subscriber was missing for ${subscription.id}.")
        val creatorId = subscription.metadata["creatorId"]
            ?: error("Creator was missing for ${subscription.id}.")
        val creatorCountry = subscription.metadata["creatorCountry"]
            ?: error("Creator $creatorId must have its country set.")
        val tierId = subscription.discount.coupon.metadata["tierId"]
            ?: error("Tier id was not properly set within coupon ${coupon.id}.")
        // find the corresponding transfer to coupon account
        val transfers = stripeClients[payload.currency].transfers()
            .list(TransferListParams.builder().setTransferGroup(coupon.id).build())
            .autoPagingIterable()
        val couponTransfers = transfers.filter { it.destination == "heroCouponAccount" }
        if (couponTransfers.size != 1) {
            error(
                "There must be exactly one coupon ${coupon.id} transfer " +
                    "to Herohero account ${"heroCouponAccount"}.",
            )
        }
        val couponTransfer = couponTransfers.first()
        if (!couponTransfer.reversed) {
            couponTransfer.reversals.create(TransferReversalCollectionCreateParams.builder().build())
        }
        // find transfers to creator if already existing
        val creatorTransfers = transfers.filter { it.destination == subscription.transferData.destination }
        if (creatorTransfers.size > 1) {
            error(
                "There must be at most one transfer to creator $creatorId for " +
                    "coupon ${coupon.id} and subscription ${subscription.id}",
            )
        }
        // but there cannot be more transfers
        val invalidTransfers = transfers.minus(creatorTransfers).minus(couponTransfers)
        if (invalidTransfers.isNotEmpty()) {
            error("Find invalid transfers for coupon ${coupon.id}: $invalidTransfers")
        }

        val tier = Tier.ofId(tierId)
        val (transferPerCents, feeCents, feeVatCents) = computeFee(
            feePercents = tier.feePercents,
            creatorVatId = creatorVatId,
            creatorCountry = creatorCountry,
            instant = Instant.ofEpochSecond(subscription.created),
            countryToVatMapping = VatMapping(mapOf()),
        )

        val transferCents = transferPerCents
            ?.times(couponTransfer.amount.toBigDecimal())
            ?.divide(BigDecimal(100))
            ?.toLong()

        val creatorTransfer = creatorTransfers.firstOrNull()
        if (creatorTransfer != null) {
            // transfer to the creator has already been performed, just check that the amounts correspond
            if (creatorTransfer.amount != transferCents) {
                error(
                    "Trying to transfer different amount (${creatorTransfer.amount} != $transferCents) " +
                        "to previously transferred amount for subscription ${subscription.id}.",
                )
            }
            log.info(
                "Amount $transferCents for ${coupon.id} for ${subscription.id} was already transferred" +
                    " in ${creatorTransfer.id} to $creatorId, skipping.",
            )
            return
        }

        log.info(
            "Coupon ${coupon.id} was applied, transferring $transferCents of ${couponTransfer.amount}" +
                " to ${subscription.transferData.destination} ($creatorId).",
            mapOf("creatorId" to creatorId, "userId" to subscription.metadata["userId"]),
        )

        stripeClients[payload.currency].transfers().create(
            TransferCreateParams.builder()
                .setAmount(transferCents)
                .setCurrency(couponTransfer.currency)
                .setDestination(subscription.transferData.destination)
                .setTransferGroup(coupon.id)
                .putAllMetadata(
                    mapOf(
                        Subscriber::creatorId.name to creatorId,
                        Subscriber::userId.name to userId,
                        Subscriber::tierId.name to tier.id,
                        Subscriber::couponId.name to coupon?.id,
                        Subscriber::couponAppliedForMonths.name to coupon?.durationInMonths?.toString(),
                        "type" to PaymentType.COUPON.name.lowercase(),
                        "priceCents" to tier.priceCents.toString(),
                        "feeCents" to feeCents.toString(),
                        "feeVatCents" to feeVatCents.toString(),
                        "transferCents" to transferCents.toString(),
                        "creatorCountry" to creatorCountry,
                        "creatorVatId" to creatorVatId,
                    ),
                )
                .build(),
        )
    }
}
