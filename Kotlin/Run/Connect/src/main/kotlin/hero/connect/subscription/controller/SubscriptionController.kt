package hero.connect.subscription.controller

import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.parseToken
import hero.http4k.extensions.body
import hero.http4k.extensions.get
import hero.jwt.jwtParser
import hero.model.OAuthScopes
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.jooq.Tables.SUBSCRIPTION
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.jooq.DSLContext
import java.time.Instant
import javax.crypto.SecretKey

fun subscriptionController(
    secretKey: <PERSON><PERSON>ey,
    lazyContext: Lazy<DSLContext>,
) = ("/v1/subscription").get(
    summary = "Get subscription detail of authenticated user",
    tag = "Subscriptions",
    parameters = object {},
    responses = listOf(Status.OK to ConnectSubscriptionResponse(Instant.now(), ConnectSubscriptionStatus.ACTIVE)),
    handler = { request, _ ->
        val token = request.getBearerToken(secretKey)

        if (OAuthScopes.SUBSCRIPTION_READ !in token.scopes) {
            throw ForbiddenException()
        }

        val context = lazyContext.value

        val subscription = context.selectFrom(SUBSCRIPTION)
            .where(SUBSCRIPTION.USER_ID.eq(token.userId))
            .and(SUBSCRIPTION.CREATOR_ID.eq(token.creatorId))
            .orderBy(SUBSCRIPTION.UPDATED_AT.desc())
            .fetchOne()

        val response = subscription?.let {
            ConnectSubscriptionResponse(
                subscribedAt = it.startedAt,
                status = when {
                    it.status in JooqSubscriptionHelper.activeStatuses -> ConnectSubscriptionStatus.ACTIVE
                    else -> ConnectSubscriptionStatus.EXPIRED
                },
            )
        } ?: ConnectSubscriptionResponse(
            subscribedAt = null,
            status = ConnectSubscriptionStatus.NEVER_SUBSCRIBED,
        )

        Response(Status.OK).body(response)
    },
)

fun Request.getBearerToken(secretKey: SecretKey): ConnectBearerToken {
    val authorizationHeader = header("Authorization") ?: throw UnauthorizedException()
    val bearerDelimiter = "Bearer "
    if (!authorizationHeader.startsWith(bearerDelimiter)) {
        throw UnauthorizedException()
    }

    val token = authorizationHeader.substringAfter(bearerDelimiter)
    val claims = parseToken(token, jwtParser(secretKey = secretKey)) ?: throw UnauthorizedException()

    val scopes = (claims["scopes"] as? List<*>)
        ?.mapNotNull { it as? String }
        ?.map { OAuthScopes.parse(it) }
        ?: emptyList()

    return ConnectBearerToken(
        claims.getValue("sub").toString(),
        claims.getValue("clientId").toString(),
        claims.getValue("creatorId").toString(),
        scopes,
    )
}

data class ConnectBearerToken(
    val userId: String,
    val clientId: String,
    val creatorId: String,
    val scopes: List<OAuthScopes>,
)

data class ConnectSubscriptionResponse(
    val subscribedAt: Instant?,
    val status: ConnectSubscriptionStatus,
)

enum class ConnectSubscriptionStatus {
    ACTIVE,
    EXPIRED,
    NEVER_SUBSCRIBED,
}
