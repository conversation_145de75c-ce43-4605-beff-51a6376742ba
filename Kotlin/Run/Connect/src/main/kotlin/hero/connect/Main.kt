package hero.connect

import hero.baseutils.SystemEnv
import hero.baseutils.systemEnv
import hero.connect.oauth.OAuthAccessTokenGenerator
import hero.connect.oauth.OAuthAuthorizationCodeService
import hero.connect.oauth.OAuthClientCredentialsValidator
import hero.connect.oauth.OAuthClientValidator
import hero.connect.oauth.OAuthRequestTracker
import hero.connect.oauth.authorizationDetailsController
import hero.connect.oauth.authorizationServer
import hero.connect.subscription.controller.subscriptionController
import hero.http4k.http4kInJettyContracts
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import io.jsonwebtoken.security.Keys
import java.security.SecureRandom

fun main() {
    val lazyContext = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) }
    val requestValidator = OAuthClientValidator(lazyContext)
    val secretKey = systemEnv("CONNECT_SECRET_KEY").let { Keys.hmacShaKeyFor(it.toByteArray()) }
    val authRequestTracking = OAuthRequestTracker(secretKey)
    val random = SecureRandom()
    val authorizationCodes = OAuthAuthorizationCodeService(lazyContext, random)
    val accessTokenAuthentication = OAuthClientCredentialsValidator(lazyContext)
    val accessTokens = OAuthAccessTokenGenerator(lazyContext, random, secretKey)
    val isLocalHost = systemEnv("LOG_APPENDER") != "ConsoleFluentD"

    val authorizationServer = authorizationServer(
        requestValidator,
        authRequestTracking,
        authorizationCodes,
        accessTokenAuthentication,
        accessTokens,
        "${SystemEnv.hostname}/oauth-login",
    )

    val authorizationDetailsController = authorizationDetailsController(lazyContext, authRequestTracking)
    val subscriptionController = subscriptionController(secretKey, lazyContext)
    http4kInJettyContracts(
        "connect",
        SystemEnv.isProduction,
        isLocalHost,
        authorizationServer + authorizationDetailsController + subscriptionController,
    )
}
