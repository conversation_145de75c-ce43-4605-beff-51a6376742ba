package hero.stripe.payout.service

import com.stripe.model.BalanceTransaction
import com.stripe.model.Charge
import com.stripe.model.Charge.TransferData
import com.stripe.model.Payout
import com.stripe.model.Refund
import com.stripe.model.Transfer
import com.stripe.param.reporting.ReportRunCreateParams.Parameters.ReportingCategory
import hero.baseutils.systemEnv
import hero.model.CZ_VAT_COUNTRY
import hero.model.Currency
import hero.model.PaymentType
import hero.model.Tier
import hero.model.euCountries
import hero.stripe.service.StripeService
import hero.stripe.service.VatMappingProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.math.BigDecimal
import java.time.Instant
import kotlin.test.assertEquals

class PayoutCommandServiceTest {
    private val stripe: StripeService = mockk()

    private val vatMappingProvider = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD"))

    private val underTest = PayoutCommandService(
        invoicesCollection = mockk(),
        hostnameServices = "https://svc-devel.herohero.co",
        pubSub = mockk(),
        stripe = stripe,
        usersCollection = mockk(),
        tiersCollection = mockk(),
        countryToVatMapping = vatMappingProvider.countryToVatMapping(),
    )

    private val countryToVatMapping = vatMappingProvider.countryToVatMapping()

    private val tierEur: Tier = Tier.ofId("EUR05")
    private val testAccountId = "acct_123456"
    private val testPayoutId = "po_789456"

    @AfterEach
    fun afterEach() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `restore intentionally broken emails for invoice delivery`() {
        assertEquals(
            "<EMAIL>",
            "<EMAIL>".restoreEmail(),
        )
        assertEquals(
            "<EMAIL>",
            "<EMAIL>-2023-12-29T19:06:18".restoreEmail(),
        )
        assertEquals(
            "<EMAIL>",
            "<EMAIL>-2023-12-29T19:06:18.749532233Z".restoreEmail(),
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "true,CZ,null,1138,138",
            "true,CZ,CZ123456,1138,138",
            "true,DE,null,1135,135",
            "true,DE,DE123456,1111,111",
            "true,UK,null,1111,111",
            "true,UK,UK123456789,1111,111",
            "true,US,null,1111,111",
            "false,CZ,null,1138,138",
            "false,CZ,CZ123456,1138,138",
            "false,DE,null,1135,135",
            "false,DE,DE123456,1111,111",
            "false,UK,null,1111,111",
            "false,UK,UK123456789,1111,111",
            "false,US,null,1111,111",
        ],
        nullValues = ["null"],
    )
    fun `payout with positive other adjustment is correctly processed`(
        automaticPayout: Boolean,
        country: String,
        vatId: String?,
        expectedAmount: BigDecimal,
        expectedFee: BigDecimal,
    ) {
        val now = Instant.now()
        val payout = payout(
            timestamp = now,
            automaticPayout = automaticPayout,
            tier = tierEur,
            transactions = listOf(
                transaction(
                    amountCents = 10_00L,
                    currencyParam = tierEur.currency,
                    feeCents = tierEur.feePercents,
                    category = ReportingCategory.OTHER_ADJUSTMENT,
                    isRefund = false,
                    transactionDescription = "Manual conversion from €10.00",
                    timestamp = now,
                ),
            ),
        )
        val (invoiceItems, charges) = underTest.processPayout(
            testAccountId,
            payout,
            tierEur,
            country,
            Currency.EUR,
            vatId,
            false,
        )

        val withVat = country == CZ_VAT_COUNTRY || (country in euCountries && vatId == null)
        assertEquals(
            listOf(
                PayoutGroupCounted(
                    group = PayoutGroup(
                        amountCents = expectedAmount,
                        heroheroFeeCents = expectedFee,
                        stripeFee = BigDecimal.ZERO,
                        stripeFeeFixed = BigDecimal.ZERO,
                        currency = tierEur.currency,
                        isRefund = false,
                        type = PaymentType.OTHER_TRANSACTION,
                        feePerCents = BigDecimal("10.00"),
                        fetVatPerCents = if (withVat)
                            countryToVatMapping[country, now].toBigDecimal()
                        else
                            BigDecimal.ZERO,
                    ),
                    count = 1,
                ),
            ),
            invoiceItems,
        )
        assertEquals(1, charges)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "true,CZ,null,-1138,-138",
            "true,CZ,CZ123456,-1138,-138",
            "true,DE,null,-1135,-135",
            "true,DE,DE123456,-1111,-111",
            "true,UK,null,-1111,-111",
            "true,UK,UK123456789,-1111,-111",
            "true,US,null,-1111,-111",
            "false,CZ,null,-1138,-138",
            "false,CZ,CZ123456,-1138,-138",
            "false,DE,null,-1135,-135",
            "false,DE,DE123456,-1111,-111",
            "false,UK,null,-1111,-111",
            "false,UK,UK123456789,-1111,-111",
            "false,US,null,-1111,-111",
        ],
        nullValues = ["null"],
    )
    fun `payout with negative other adjustment is correctly processed`(
        automaticPayout: Boolean,
        country: String,
        vatId: String?,
        expectedAmount: BigDecimal,
        expectedFee: BigDecimal,
    ) {
        val now = Instant.now()
        val payout = payout(
            timestamp = now,
            automaticPayout = automaticPayout,
            tier = tierEur,
            transactions = listOf(
                transaction(
                    amountCents = -10_00L,
                    currencyParam = tierEur.currency,
                    feeCents = tierEur.feePercents,
                    category = ReportingCategory.OTHER_ADJUSTMENT,
                    isRefund = false,
                    transactionDescription = "Manual conversion from €10.00",
                    timestamp = Instant.now(),
                ),
            ),
        )
        val (invoiceItems, charges) = underTest.processPayout(
            testAccountId,
            payout,
            tierEur,
            country,
            Currency.EUR,
            vatId,
            false,
        )

        val withVat = country == CZ_VAT_COUNTRY || (country in euCountries && vatId == null)
        assertEquals(
            listOf(
                PayoutGroupCounted(
                    group = PayoutGroup(
                        amountCents = expectedAmount,
                        heroheroFeeCents = expectedFee,
                        stripeFee = BigDecimal.ZERO,
                        stripeFeeFixed = BigDecimal.ZERO,
                        currency = tierEur.currency,
                        isRefund = false,
                        type = PaymentType.OTHER_TRANSACTION,
                        feePerCents = BigDecimal("10.00"),
                        fetVatPerCents = if (withVat)
                            countryToVatMapping[country, now].toBigDecimal()
                        else
                            BigDecimal.ZERO,
                    ),
                    count = 1,
                ),
            ),
            invoiceItems,
        )
        assertEquals(1, charges)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "CZ,null",
            "CZ,DE123456",
            "DE,null",
            "DE,DE123456",
            "ES,null",
            "ES,ES123456",
            "UK,null",
            "UK,UK123456789",
            "US,null",
        ],
        nullValues = ["null"],
    )
    fun `charges are correctly grouped`(
        country: String,
        vatId: String?,
    ) {
        val now = Instant.now()
        val payout = payout(
            timestamp = now,
            automaticPayout = true,
            tier = tierEur,
            transactions = listOf(
                transaction(
                    10_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    false,
                    "Charge €10.00",
                    now,
                ),
                transaction(
                    10_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    false,
                    "Charge €10.00",
                    now,
                ),
                transaction(
                    10_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    true,
                    "Refund €10.00",
                    now,
                ),
                transaction(
                    10_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    true,
                    "Refund €10.00",
                    now,
                ),
                transaction(
                    10_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    true,
                    "Refund €10.00",
                    now,
                ),
                transaction(
                    20_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    false,
                    "Charge €20.00",
                    now,
                ),
                transaction(
                    20_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    false,
                    "Charge €20.00",
                    now,
                ),
                transaction(
                    20_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    false,
                    "Charge €20.00",
                    now,
                ),
                transaction(
                    20_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.CHARGE,
                    true,
                    "Refund €20.00",
                    now,
                ),
                // payout transactions are ignored
                transaction(
                    20_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.PAYOUT,
                    false,
                    "Payout €100.00",
                    now,
                ),
                transaction(
                    20_00L,
                    tierEur.currency,
                    tierEur.feePercents,
                    ReportingCategory.PAYOUT_REVERSAL,
                    true,
                    "Payout reversal €100.00",
                    now,
                ),
            ),
        )
        val (invoiceItems, charges) = underTest.processPayout(
            testAccountId,
            payout,
            tierEur,
            country,
            Currency.EUR,
            vatId,
            false,
        )

        val expectedVat = if (country == CZ_VAT_COUNTRY || (country in euCountries && vatId == null))
            countryToVatMapping[country, now].toBigDecimal()
        else
            BigDecimal.ZERO

        assertEquals(
            listOf(
                PayoutGroupCounted(
                    group = PayoutGroup(
                        amountCents = 10_00.toBigDecimal(),
                        heroheroFeeCents = 1_00.toBigDecimal(),
                        stripeFee = BigDecimal.ZERO,
                        stripeFeeFixed = BigDecimal.ZERO,
                        currency = tierEur.currency,
                        isRefund = false,
                        type = PaymentType.SUBSCRIPTION,
                        feePerCents = BigDecimal("10.00"),
                        fetVatPerCents = expectedVat,
                    ),
                    count = 2,
                ),
                PayoutGroupCounted(
                    group = PayoutGroup(
                        amountCents = 10_00.toBigDecimal(),
                        heroheroFeeCents = 1_00.toBigDecimal(),
                        stripeFee = BigDecimal.ZERO,
                        stripeFeeFixed = BigDecimal.ZERO,
                        currency = tierEur.currency,
                        isRefund = true,
                        type = PaymentType.SUBSCRIPTION,
                        feePerCents = BigDecimal("10.00"),
                        fetVatPerCents = expectedVat,
                    ),
                    count = 3,
                ),
                PayoutGroupCounted(
                    group = PayoutGroup(
                        20_00.toBigDecimal(),
                        2_00.toBigDecimal(),
                        stripeFee = BigDecimal.ZERO,
                        stripeFeeFixed = BigDecimal.ZERO,
                        tierEur.currency,
                        false,
                        PaymentType.SUBSCRIPTION,
                        BigDecimal("10.00"),
                        expectedVat,
                    ),
                    count = 3,
                ),
                PayoutGroupCounted(
                    group = PayoutGroup(
                        20_00.toBigDecimal(),
                        2_00.toBigDecimal(),
                        stripeFee = BigDecimal.ZERO,
                        stripeFeeFixed = BigDecimal.ZERO,
                        tierEur.currency,
                        true,
                        PaymentType.SUBSCRIPTION,
                        BigDecimal("10.00"),
                        expectedVat,
                    ),
                    count = 1,
                ),
            ),
            invoiceItems,
        )
        assertEquals(9, charges)
    }

    @Test
    fun `legacy fee is correctly processed`() {
        val payoutItem = underTest.processBalanceTransaction(
            it = transaction(
                amountCents = 10_00L,
                currencyParam = tierEur.currency,
                feeCents = tierEur.feePercents,
                category = ReportingCategory.CHARGE,
                isRefund = false,
                transactionDescription = "Charge €10.00",
                timestamp = Instant.now(),
                legacyFee = true,
            ),
            accountId = testAccountId,
            payoutId = testPayoutId,
            creatorsTier = tierEur,
            creatorsVatId = "CZ123456",
            country = CZ_VAT_COUNTRY,
            currency = tierEur.currency,
        )
        assertEquals(
            expected = PayoutGroup(
                amountCents = 10_00.toBigDecimal(),
                heroheroFeeCents = 1_00.toBigDecimal(),
                stripeFee = BigDecimal.ZERO,
                stripeFeeFixed = BigDecimal.ZERO,
                currency = tierEur.currency,
                isRefund = false,
                type = PaymentType.SUBSCRIPTION,
                feePerCents = BigDecimal("10.00"),
                fetVatPerCents = 21.toBigDecimal(),
            ),
            actual = payoutItem,
        )
    }

    private fun payout(
        automaticPayout: Boolean,
        transactions: List<BalanceTransaction>,
        tier: Tier,
        timestamp: Instant,
    ): Payout {
        val payout = Payout().also {
            it.id = testPayoutId
            it.automatic = automaticPayout
            it.amount = transactions.sumOf { it.amount }
            it.currency = tier.currency.name.lowercase()
            it.created = timestamp.epochSecond
        }
        every { stripe.listPayoutTransactions(testAccountId, testPayoutId, tier.currency) } returns transactions
        return payout
    }

    private fun transaction(
        amountCents: Long,
        currencyParam: Currency,
        feeCents: BigDecimal,
        category: ReportingCategory,
        isRefund: Boolean,
        transactionDescription: String,
        timestamp: Instant,
        legacyFee: Boolean = false,
    ): BalanceTransaction {
        val payoutChargeId = "py_123456_${amountCents}_${timestamp.toEpochMilli() + 1}"
        val originalChargeId = "py_789123_${amountCents}_${timestamp.toEpochMilli() + 2}"
        val refundChargeId = if (isRefund) "pyr_123456_${timestamp.toEpochMilli() + 3}" else null

        val transferredCents = BigDecimal(amountCents)
            .times((100.toBigDecimal() - feeCents))
            .div(BigDecimal(100))
            .toLong()

        val originalCharge = Charge().also { charge ->
            charge.id = originalChargeId
            charge.created = timestamp.epochSecond
            charge.amount = amountCents
            charge.currency = currencyParam.name.lowercase()
            charge.metadata = mapOf<String, String>()
            if (legacyFee) {
                @Suppress("ktlint:standard:max-line-length")
                charge.applicationFeeAmount = amountCents.toBigDecimal().times(
                    feeCents.divide(BigDecimal(100)),
                ).toLong()
                charge.transferData = TransferData().also {
                    it.amount = null
                }
            } else {
                charge.transferData = TransferData().also {
                    it.amount = transferredCents
                }
            }
        }

        val accountCharge = Charge().also {
            it.id = payoutChargeId
            it.created = timestamp.epochSecond
            it.amount = transferredCents
            it.currency = currencyParam.name.lowercase()
            it.sourceTransferObject = Transfer().also { transfer ->
                transfer.created = timestamp.epochSecond
                transfer.amount = transferredCents
                transfer.currency = currencyParam.name.lowercase()
                transfer.sourceTransaction = originalChargeId
                transfer.sourceTransactionObject = originalCharge
            }
        }

        val accountRefund = Refund().also {
            it.id = refundChargeId
            it.created = timestamp.epochSecond
            it.amount = transferredCents
            it.currency = currencyParam.name.lowercase()
            it.chargeObject = accountCharge
        }

        if (isRefund) {
            every { stripe.charge(originalChargeId, tierEur.currency) } returns originalCharge
        }

        val transaction = BalanceTransaction().also {
            it.id = "bt_" + System.currentTimeMillis()
            it.amount = amountCents
            it.reportingCategory = category.name.lowercase()
            it.sourceObject = if (isRefund) accountRefund else accountCharge
            it.description = transactionDescription
            it.created = timestamp.epochSecond
            it.currency = currencyParam.name.lowercase()
        }

        return transaction
    }
}
