package hero.auth.util

import hero.baseutils.plusHours
import hero.exceptions.http.ForbiddenException
import hero.jwt.JwtUser
import hero.jwt.TOKEN_TYPE_CLAIM
import hero.jwt.TokenType
import hero.jwt.generateJwt
import hero.jwt.jwtParser
import hero.jwt.toJwt
import io.jsonwebtoken.Claims
import java.time.Instant

fun deleteToken(
    userId: String,
    expiry: Long = Instant.now().plusHours(2).epochSecond,
): String = JwtUser(userId, expiry, 0).toJwt(type = TokenType.DELETE)

fun validateDeleteToken(
    requesterId: String,
    deleteToken: String,
) {
    validateToken(requesterId, deleteToken, TokenType.DELETE)
}

fun verifyEmailToken(
    userId: String,
    expiry: Long = Instant.now().plusHours(2).epochSecond,
): String = JwtUser(userId, expiry, 0).toJwt(type = TokenType.VERIFY_EMAIL)

fun changeEmailToken(
    userId: String,
    email: String,
    expiry: Long = Instant.now().plusHours(2).epochSecond,
): String = generateJwt(userId, expiry, TokenType.CHANGE_EMAIL, additionalClaims = mapOf("email" to email))

fun validateVerifyEmailToken(
    requesterId: String,
    verifyEmailToken: String,
) {
    validateToken(requesterId, verifyEmailToken, TokenType.VERIFY_EMAIL)
}

fun validateChangeEmailToken(
    requesterId: String,
    changeEmailToken: String,
): String {
    val claims = validateToken(requesterId, changeEmailToken, TokenType.CHANGE_EMAIL)
    return claims["email"].toString()
}

private fun validateToken(
    requesterId: String,
    token: String,
    expectedTokenType: TokenType,
): Claims {
    try {
        val claims = jwtParser().parseSignedClaims(token).payload
        if (claims[TOKEN_TYPE_CLAIM] != expectedTokenType.name) {
            throw ForbiddenException("Invalid token type")
        }

        if (claims.subject != requesterId) {
            throw ForbiddenException("Requester $requesterId does not match ${claims.subject}")
        }

        return claims
    } catch (e: Exception) {
        throw ForbiddenException()
    }
}
