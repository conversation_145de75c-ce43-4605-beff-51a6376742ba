package hero.api.subscriber.service

import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class SubscribeRequestQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetSubscribeRequests {
        @Test
        fun `should return user's subscribe requests sorted by created at desc`() {
            val underTest = SubscribeRequestQueryService(lazyTestContext)

            val now = Instant.now()
            testHelper.createUser("cestmir")
            testHelper.createUser("user1")
            testHelper.createUser("user2")

            val request1 = testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "cestmir",
                createdAt = now,
            )
            val request2 = testHelper.createSubscribeRequest(
                userId = "user2",
                creatorId = "cestmir",
                createdAt = now - 5.seconds,
            )

            val result = underTest.execute(GetSubscribeRequests("cestmir", PageRequest()))

            assertThat(result.hasNext).isFalse()
            assertThat(result.content).containsExactly(request1, request2)
        }

        @Test
        fun `should fetch only user's subscribe requests and not other users`() {
            val underTest = SubscribeRequestQueryService(lazyTestContext)

            testHelper.createUser("creator1")
            testHelper.createUser("creator2")
            testHelper.createUser("user")

            val request1 = testHelper.createSubscribeRequest(
                userId = "user",
                creatorId = "creator1",
            )
            testHelper.createSubscribeRequest(
                userId = "user",
                creatorId = "creator2",
            )

            val result = underTest.execute(GetSubscribeRequests("creator1", PageRequest()))

            assertThat(result.content).containsExactly(request1)
        }

        @Test
        fun `should correctly page through user's subscribe requests using afterCursor`() {
            val underTest = SubscribeRequestQueryService(lazyTestContext)

            val now = Instant.now()
            testHelper.createUser("cestmir")
            testHelper.createUser("user1")
            testHelper.createUser("user2")
            testHelper.createUser("user3")

            val request1 = testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "cestmir",
                createdAt = now,
            )
            val request2 = testHelper.createSubscribeRequest(
                userId = "user2",
                creatorId = "cestmir",
                createdAt = now - 5.seconds,
            )
            val request3 = testHelper.createSubscribeRequest(
                userId = "user3",
                creatorId = "cestmir",
                createdAt = now - 10.seconds,
            )

            // First page with pageSize = 1
            val afterPageable1 = PageRequest(pageSize = 1)
            val afterPage1 = underTest.execute(GetSubscribeRequests("cestmir", afterPageable1))

            // going through requests from the newest to the oldest
            assertThat(afterPage1.hasNext).isTrue()
            assertThat(afterPage1.content).containsExactly(request1)

            // Second page using afterCursor
            val afterPageable2 = PageRequest(pageSize = 1, afterCursor = afterPage1.nextPageable.afterCursor)
            val afterPage2 = underTest.execute(GetSubscribeRequests("cestmir", afterPageable2))

            assertThat(afterPage2.hasNext).isTrue()
            assertThat(afterPage2.content).containsExactly(request2)

            // Third page using afterCursor
            val afterPageable3 = PageRequest(pageSize = 1, afterCursor = afterPage2.nextPageable.afterCursor)
            val afterPage3 = underTest.execute(GetSubscribeRequests("cestmir", afterPageable3))

            assertThat(afterPage3.hasNext).isFalse()
            assertThat(afterPage3.content).containsExactly(request3)
        }

        @Test
        fun `should include only non-accepted, non-deleted, non-declined`() {
            val underTest = SubscribeRequestQueryService(lazyTestContext)

            val now = Instant.now()
            testHelper.createUser("cestmir")
            testHelper.createUser("user1")
            testHelper.createUser("user2")
            testHelper.createUser("user3")
            testHelper.createUser("user4")

            val pendingRequest = testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "cestmir",
                createdAt = now,
            )
            testHelper.createSubscribeRequest(
                userId = "user2",
                creatorId = "cestmir",
                createdAt = now - 5.seconds,
                acceptedAt = now,
            )
            testHelper.createSubscribeRequest(
                userId = "user3",
                creatorId = "cestmir",
                createdAt = now - 10.seconds,
                declinedAt = now,
            )
            testHelper.createSubscribeRequest(
                userId = "user4",
                creatorId = "cestmir",
                createdAt = now - 15.seconds,
                deletedAt = now,
            )

            val result = underTest.execute(GetSubscribeRequests("cestmir", PageRequest()))

            assertThat(result.content).containsExactly(pendingRequest)
        }
    }

    @Nested
    inner class FindLastSubscribeRequest {
        @Test
        fun `should return null when no subscribe request exists for user-creator pair`() {
            val underTest = SubscribeRequestQueryService(lazyTestContext)

            testHelper.createUser("user1")
            testHelper.createUser("creator1")

            val result = underTest.execute(FindLastSubscribeRequest("creator1", "user1"))

            assertThat(result).isNull()
        }

        @Test
        fun `should return only subscribe requests for the specific user-creator pair`() {
            val underTest = SubscribeRequestQueryService(lazyTestContext)

            val now = Instant.now()
            testHelper.createUser("user1")
            testHelper.createUser("user2")
            testHelper.createUser("creator1")
            testHelper.createUser("creator2")

            // Create subscribe requests for different user-creator pairs
            testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "creator2",
                createdAt = now,
            )
            testHelper.createSubscribeRequest(
                userId = "user2",
                creatorId = "creator1",
                createdAt = now,
            )
            val targetRequest = testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "creator1",
                createdAt = now - 5.seconds,
            )

            val result = underTest.execute(FindLastSubscribeRequest("creator1", "user1"))

            assertThat(result).isEqualTo(targetRequest)
        }

        @Test
        fun `should return the most recent request even when multiple requests exist with different statuses`() {
            val underTest = SubscribeRequestQueryService(lazyTestContext)

            val now = Instant.now()
            testHelper.createUser("user1")
            testHelper.createUser("creator1")

            // Create subscribe requests with different statuses
            testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "creator1",
                createdAt = now - 15.seconds,
                acceptedAt = now - 10.seconds,
            )
            testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "creator1",
                createdAt = now - 10.seconds,
                declinedAt = now - 5.seconds,
            )
            val mostRecentRequest = testHelper.createSubscribeRequest(
                userId = "user1",
                creatorId = "creator1",
                createdAt = now,
                deletedAt = null,
            )

            val result = underTest.execute(FindLastSubscribeRequest("creator1", "user1"))

            assertThat(result).isEqualTo(mostRecentRequest)
        }
    }
}
