package hero.api.subscriber.service

import hero.exceptions.http.ConflictException
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.time.TestClock
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import java.time.Instant

class SubscribeRequestCommandServiceIT : IntegrationTest() {
    @Test
    fun `should create subscribe request`() {
        val now = Instant.ofEpochSecond(1747831124)
        val underTest = SubscribeRequestCommandService(
            TestRepositories.subscribeRequestRepository,
            TestRepositories.userRepository,
            mockk(),
            mockk(),
            TestRepositories.notificationRepository,
            lazyTestContext,
            TestClock(now),
        )
        testHelper.createUser("cestmir")
        testHelper.createUser("pablo")

        val subscribeRequest = underTest.execute(RequestToSubscribe("cestmir", "pablo"))

        val createdSubscribeRequest = TestRepositories.subscribeRequestRepository.getById(subscribeRequest.id)

        assertThat(subscribeRequest).isEqualTo(createdSubscribeRequest)
        assertThat(subscribeRequest.createdAt).isEqualTo(now)
        assertThat(subscribeRequest.userId).isEqualTo("pablo")
        assertThat(subscribeRequest.creatorId).isEqualTo("cestmir")
        assertThat(subscribeRequest.deletedAt).isNull()
        assertThat(subscribeRequest.declinedAt).isNull()
        assertThat(subscribeRequest.acceptedAt).isNull()
    }

    @Test
    fun `cannot create subscribe request if user already subscribes the creator`() {
        val now = Instant.ofEpochSecond(1747831124)
        val underTest = SubscribeRequestCommandService(
            TestRepositories.subscribeRequestRepository,
            TestRepositories.userRepository,
            mockk(),
            mockk(),
            TestRepositories.notificationRepository,
            lazyTestContext,
            TestClock(now),
        )
        testHelper.createUser("cestmir")
        testHelper.createUser("pablo")
        testHelper.createSubscription("pablo", "cestmir")

        assertThatExceptionOfType(ConflictException::class.java).isThrownBy {
            underTest.execute(RequestToSubscribe("cestmir", "pablo"))
        }
    }
}
