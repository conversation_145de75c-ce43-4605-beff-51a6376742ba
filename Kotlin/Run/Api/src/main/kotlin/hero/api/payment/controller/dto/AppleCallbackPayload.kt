package hero.api.payment.controller.dto

import com.apple.itunes.storekit.model.NotificationTypeV2
import com.apple.itunes.storekit.model.Subtype
import hero.model.Currency
import java.time.Instant

data class AppleCallbackPayload(
    val transactionId: String,
    /** related to advanced commerce api */
    val requestReferenceId: String?,
    val notificationType: NotificationTypeV2,
    val subType: Subtype?,
    val userId: String,
    val creatorId: String,
    val tierId: String,
    val purchaseDate: Instant,
    val expiresDate: Instant,
    val storefront: String?,
    val type: String?,
    val price: Int,
    val currency: Currency,
)
