package hero.api.payment.service

import com.github.kittinunf.fuel.httpPost
import com.stripe.model.Coupon
import com.stripe.param.CouponCreateParams
import hero.api.payment.controller.PaymentResponse
import hero.api.payment.controller.dto.AppleRefundType
import hero.api.payment.controller.dto.AppleRequestInfo
import hero.api.payment.controller.dto.AppleSubscriptionCancelRequest
import hero.api.payment.controller.dto.AppleSubscriptionRevokeRequest
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.exceptions.http.ConflictException
import hero.jackson.toJson
import hero.model.CouponMethod
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.model.topics.CardCreateType
import hero.stripe.service.StripeClients
import java.time.Instant
import java.util.UUID

// TODO use https://github.com/apple/app-store-server-library-java for verifications
class StripeAppleService(
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val clients: StripeClients,
    private val userRepository: UsersRepository,
) {
    private fun appleCouponFactory(
        userId: String,
        creator: User,
        tier: Tier,
    ): Coupon {
        val price = subscriberStripeRepository.priceFactory(creator, tier)
        return clients[tier.currency].coupons().create(
            CouponCreateParams.builder()
                .setId("APPLE-${tier.currency}-${UUID.randomUUID()}")
                .setDuration(CouponCreateParams.Duration.FOREVER)
                .setAppliesTo(
                    CouponCreateParams.AppliesTo.builder()
                        .addProduct(price.product)
                        .build(),
                )
                .setPercentOff(100.toBigDecimal())
                .setMetadata(
                    mapOf(
                        "purchasedByUserId" to userId,
                        "couponMethod" to CouponMethod.APPLE_IN_APP.name,
                        "creatorId" to creator.id,
                    ),
                )
                .build(),
        )
    }

    fun createSubscription(
        appleTransactionId: String,
        userId: String,
        creatorId: String,
        tierId: String,
    ): PaymentResponse {
        val tier = Tier.ofId(tierId)
        val user = userRepository.get(userId)
        val creator = userRepository.get(creatorId)

        if (!creator.creator.active) {
            throw ConflictException(
                "Creator ${creator.id} has not finished their Stripe account pairing.",
                mapOf("creatorId" to creator.id, "appleTransactionId" to appleTransactionId),
            )
        }

        if (creator.id == user.id) {
            throw ConflictException(
                "User ${user.id} cannot subscribe themselves.",
                mapOf("userId" to user.id, "appleTransactionId" to appleTransactionId),
            )
        }

        val coupon = appleCouponFactory(userId, creator, tier)
        val subscription = subscriberStripeRepository.subscribe(
            user = user,
            creator = creator,
            paymentMethodId = null,
            couponId = coupon.id,
            cardCreateType = CardCreateType.APPLE_IN_APP,
            metadata = mapOf(Subscriber::appleTransactionId.name to appleTransactionId),
        )

        log.info(
            "Replicated Apple subscription $appleTransactionId of ${user.id} -> ${creator.id}" +
                " as ${subscription.attributes.subscriptionStatus}",
            mapOf("userId" to user.id, "creatorId" to creator.id, "appleTransactionId" to appleTransactionId),
        )
        return subscription
    }

    fun appleCancelledAtPeriodEnd(
        transactionId: String,
        cancelAtPeriodEnd: Boolean,
        cancelAt: Instant,
    ) {
        subscriberStripeRepository.patchSubscription(transactionId, cancelAtPeriodEnd, cancelAt)
    }

    fun appleCancelledNow(transactionId: String) {
    }

    // https://developer.apple.com/documentation/advancedcommerceapi/migrate-subscription-to-advanced-commerce-api
    fun migrateToAdvanced(transactionId: String) {
        "https://api.storekit.itunes.apple.com/advancedCommerce/v1/subscription/migrate/$transactionId"
            .httpPost()
    }

    // Immediate cancel and refund.
    // https://developer.apple.com/documentation/advancedcommerceapi/revoke-subscription
    fun revokeSubscription(
        transactionId: String,
        refundReason: String,
    ) {
        "https://api.storekit.itunes.apple.com/advancedCommerce/v1/subscription/revoke/$transactionId"
            .httpPost()
            .body(
                AppleSubscriptionRevokeRequest(
                    refundReason = refundReason,
                    refundRiskingPreference = true,
                    refundType = AppleRefundType.FULL,
                    requestInfo = AppleRequestInfo(
                        requestReferenceId = "",
                    ),
                    storeFront = null,
                ).toJson(),
            )
    }

    // Cancel at period end.
    // https://developer.apple.com/documentation/advancedcommerceapi/cancel-a-subscription
    fun cancelSubscription(transactionId: String) {
        // sigining app store requests https://developer.apple.com/documentation/storekit/generating-jws-to-sign-app-store-requests

        "https://api.storekit.itunes.apple.com/advancedCommerce/v1/subscription/cancel/$transactionId"
            .httpPost()
            .body(
                AppleSubscriptionCancelRequest(
                    AppleRequestInfo(
                        requestReferenceId = "",
                    ),
                    storeFront = null,
                ).toJson(),
            )
    }
}
