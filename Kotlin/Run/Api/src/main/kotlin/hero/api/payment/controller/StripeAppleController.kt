package hero.api.payment.controller

import com.apple.itunes.storekit.model.NotificationTypeV2.DID_CHANGE_RENEWAL_STATUS
import com.apple.itunes.storekit.model.NotificationTypeV2.DID_RENEW
import com.apple.itunes.storekit.model.NotificationTypeV2.EXPIRED
import com.apple.itunes.storekit.model.NotificationTypeV2.REFUND
import com.apple.itunes.storekit.model.NotificationTypeV2.SUBSCRIBED
import com.apple.itunes.storekit.model.Subtype
import hero.api.payment.controller.dto.AppleRequestInfo
import hero.api.payment.controller.dto.Descriptors
import hero.api.payment.controller.dto.InAppOperation
import hero.api.payment.controller.dto.ProductItem
import hero.api.payment.controller.dto.ResponseBodyV2
import hero.api.payment.controller.dto.SignedPayload
import hero.api.payment.controller.dto.SubscriptionCreateRequest
import hero.api.payment.controller.dto.SubscriptionPeriod
import hero.api.payment.service.AppleJwtHandler
import hero.api.payment.service.StripeAppleService
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.baseutils.truncate
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.model.Currency
import hero.model.Tier
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.regex
import java.util.UUID

// TODO use https://github.com/apple/app-store-server-library-java for verifications
class StripeAppleController(
    private val jwtHandler: AppleJwtHandler,
    private val service: StripeAppleService,
    private val userRepository: UsersRepository,
) {
    @Suppress("unused")
    val routeAppleWebhook: ContractRoute =
        "/v1/stripe/apple-subscriptions".post(
            summary = "Handle Apple Webhooks",
            parameters = object {},
            receiving = listOf(ResponseBodyV2(signedPayload = "123456789")),
            tag = "Subscriptions",
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, _ ->
                val body = lens<ResponseBodyV2>(request)
                val payload = jwtHandler.parse(body.signedPayload)
                log.fatal(
                    "Apple notifies ${payload.notificationType}: $payload",
                    mapOf("appleTransactionId" to payload.transactionId),
                )

                when (payload.notificationType) {
                    SUBSCRIBED -> {
                        // AppleCallbackPayload(transactionId=****************, requestReferenceId=3104c750-7c26-40ed-9bfa-2adde9cf5e49, notificationType=SUBSCRIBED,
                        // subType=INITIAL_BUY, userId=qgqotlxqyaaab, creatorId=bpsvwdhdusxof, tierId=EUR300, purchaseDate=2025-06-10T10:04:24Z, expiresDate=2025-06-10T10:07:24Z,
                        // storefront=CZE, type=Auto-Renewable Subscription, price=900000, currency=CZK)
                        service.createSubscription(
                            appleTransactionId = payload.transactionId,
                            userId = payload.userId,
                            creatorId = payload.creatorId,
                            tierId = payload.tierId,
                        )
                    }
                    REFUND -> {
                        // reverse last transfer
                    }
                    DID_RENEW -> {
                        // do transfer to connected account
                    }
                    EXPIRED -> {
                        // AppleCallbackPayload(transactionId=****************, requestReferenceId=3104c750-7c26-40ed-9bfa-2adde9cf5e49, notificationType=EXPIRED,
                        // subType=VOLUNTARY, userId=qgqotlxqyaaab, creatorId=bpsvwdhdusxof, tierId=EUR300, purchaseDate=2025-06-10T10:04:24Z, expiresDate=2025-06-10T10:07:24Z,
                        // storefront=CZE, type=Auto-Renewable Subscription, price=900000, currency=CZK)
                        service.appleCancelledNow(payload.transactionId)
                    }
                    DID_CHANGE_RENEWAL_STATUS -> {
                        // AppleCallbackPayload(transactionId=****************, requestReferenceId=3104c750-7c26-40ed-9bfa-2adde9cf5e49, notificationType=DID_CHANGE_RENEWAL_STATUS,
                        // subType=AUTO_RENEW_DISABLED, userId=qgqotlxqyaaab, creatorId=bpsvwdhdusxof, tierId=EUR300, purchaseDate=2025-06-10T10:04:24Z, expiresDate=2025-06-10T10:07:24Z,
                        // storefront=CZE, type=Auto-Renewable Subscription, price=900000, currency=CZK)
                        val cancelAtPeriodEnd = when (payload.subType) {
                            Subtype.AUTO_RENEW_ENABLED -> false
                            Subtype.AUTO_RENEW_DISABLED -> true
                            else -> error(
                                "For $DID_CHANGE_RENEWAL_STATUS we accept only RENEW subtypes, got:" +
                                    " ${payload.subType} for ${payload.transactionId}",
                            )
                        }
                        service.appleCancelledAtPeriodEnd(payload.transactionId, cancelAtPeriodEnd, payload.expiresDate)
                    }
                    else -> {}
                }
                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routeCreateAppleSubscription: ContractRoute =
        (
            "/v1/users" / Path.userId().of("userId", "User who subscribes.") / "subscriptions-apple" /
                Path.of("creatorId", "Creator to subscribe.")
        ).post(
            parameters = object {
                val authorization = Header.authorization()
                val storefront = Query.regex("([A-Z]{3})").required("storefront")
            },
            tag = "Subscriptions",
            summary = "Create Apple subscription payload to be used in App.",
            receiving = null,
            responses = listOf(Status.OK to SignedPayload("123456789")),
            handler = { request, parameters, userId, _, creatorId ->
                val user = userRepository.get(request, userId)
                val creator = userRepository.get(creatorId)
                val tier = Tier.ofId(creator.creator.tierId)
                val productId = "${user.id.replace("-", ".")}_${creatorId.replace("-", ".")}_${tier.id}"
                val title = "${creator.name} ${tier.currency.symbol} ${tier.priceCents / 100}"
                // precise apple 15 % + VAT
                val subscriptionCoef = 0.20.toBigDecimal()
                val storefront = parameters.storefront(request)

                // TODO convert to two-letter country code and handle more correctly
                val currency = when (storefront) {
                    "CZE" -> Currency.CZK
                    "USA" -> Currency.USD
                    else -> Currency.EUR
                }

                val conversionCoef = when (currency) {
                    Currency.CZK -> 25
                    else -> 1
                }

                val priceCents = tier.priceCents.toBigDecimal()
                    .times(conversionCoef.toBigDecimal())

                // TODO check subscription does not exist yet
                val signedPayload = jwtHandler.sign(
                    SubscriptionCreateRequest(
                        operation = InAppOperation.CREATE_SUBSCRIPTION,
                        version = "1",
                        requestInfo = AppleRequestInfo(requestReferenceId = UUID.randomUUID().toString()),
                        currency = currency.name,
                        // TODO verify
                        // https://developer.apple.com/documentation/advancedcommerceapi/taxcodes
                        taxCode = "S021-08-1",
                        // https://developer.apple.com/documentation/advancedcommerceapi/descriptors
                        descriptors = Descriptors(
                            description = title.truncate(44),
                            displayName = title.truncate(29),
                        ),
                        period = SubscriptionPeriod.P1M,
                        storefront = storefront,
                        items = listOf(
                            ProductItem(
                                SKU = productId,
                                description = title.truncate(44),
                                displayName = title.truncate(29),
                                price = priceCents
                                    .plus(priceCents.times(subscriptionCoef))
                                    .toInt(),
                            ),
                        ),
                    ),
                )
                Response(Status.OK)
                    .body(SignedPayload(signedPayload))
            },
        )
}
