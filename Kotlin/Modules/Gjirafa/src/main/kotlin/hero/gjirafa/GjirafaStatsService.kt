package hero.gjirafa

import com.github.kittinunf.fuel.httpGet
import com.github.kittinunf.fuel.httpPost
import hero.baseutils.fetch
import hero.baseutils.log
import hero.gjirafa.dto.AddAudioWatchStateRequest
import hero.gjirafa.dto.AddVideoWatchStateRequest
import hero.gjirafa.dto.AudioAnalyticsGraphMetrics
import hero.gjirafa.dto.AudioKeyMomentsCardModel
import hero.gjirafa.dto.CreateAudioAnalyticsEventRequest
import hero.gjirafa.dto.CreateVideoAnalyticsEventRequest
import hero.gjirafa.dto.GjirafaIntervals
import hero.gjirafa.dto.LiveViewerResponse
import hero.gjirafa.dto.MetricsModel
import hero.gjirafa.dto.VideoAnalyticsGraphMetrics
import hero.gjirafa.dto.VideoKeyMomentsCardModelResponseModel
import hero.jackson.toJson
import hero.model.GjirafaResponse
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

class GjirafaStatsService(
    projectId: String,
    private val apiKey: String,
) {
    private val vpAnalyticsUrl = "https://analytics.vpplayer.tech/api/v2/projects/$projectId/analytics"
    private val videoAnalyticsUrl: String =
        "https://vp-bridge.gjirafa.tech/api/projects/$projectId/analytics"
    private val videoAnalyticsUrlV2: String =
        "https://vp-bridge.gjirafa.tech/api/v2/projects/$projectId/video/analytics"
    private val audioAnalyticsUrl: String =
        "https://audio-analytics.vpplayer.tech/api/v1/projects/$projectId/audio"
    private val audioAnalyticsUrlV2: String =
        "https://audio-analytics.vpplayer.tech/api/v2/projects/$projectId/analytics/audio"

    // https://vp-bridge.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Analytics%20API%20V1#/Event/post_api_projects__projectId__analytics_event
    fun postVideoAnalyticsEvent(request: CreateVideoAnalyticsEventRequest): Any =
        try {
            "$videoAnalyticsUrl/event"
                .httpPost()
                .authorize(apiKey)
                .timeout(500)
                .timeoutRead(500)
                .body(request.toJson())
                .fetch<Any>()
        } catch (e: Throwable) {
            log.error(
                "Couldn't log Gjirafa video analytics event for " +
                    "${request.userId}/${request.videoId}: ${e.message}",
            )
            mapOf("status" to 500)
        }

    // https://audio-analytics-dev.vpplayer.tech/swagger/index.html#/AudioEvent/post_api_v2_projects__projectId__analytics_audio_event
    fun postAudioAnalyticsEvent(request: CreateAudioAnalyticsEventRequest): Any =
        try {
            "$audioAnalyticsUrlV2/event"
                .httpPost()
                .authorize(apiKey)
                .timeout(500)
                .timeoutRead(500)
                .body(request.toJson())
                .fetch<Any>()
        } catch (e: Throwable) {
            log.error(
                "Couldn't log Gjirafa audio analytics event for " +
                    "${request.userId}/${request.audioId}: ${e.message}",
            )
            mapOf("status" to 500)
        }

    // https://vp-bridge.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Analytics%20API%20V1#/Event/post_analytics_watchState
    fun postVideoWatchState(request: AddVideoWatchStateRequest): Any =
        try {
            "$videoAnalyticsUrl/watchState"
                .httpPost()
                .authorize(apiKey)
                .timeout(500)
                .timeoutRead(500)
                .body(request.toJson())
                .fetch<Any>()
        } catch (e: Throwable) {
            log.error(
                "Couldn't log Gjirafa video watch state for " +
                    "${request.userId}/${request.videoId}: ${e.message}",
            )
            mapOf("status" to 500)
        }

    // https://audio-analytics-dev.vpplayer.tech/swagger/index.html#/AudioEvent/post_api_v2_projects__projectId__analytics_audio_watchState
    fun postAudioWatchState(request: AddAudioWatchStateRequest): Any =
        try {
            "$audioAnalyticsUrlV2/watchState"
                .httpPost()
                .authorize(apiKey)
                .timeout(500)
                .timeoutRead(500)
                .body(request.toJson())
                .fetch<Any>()
        } catch (e: Throwable) {
            log.error(
                "Couldn't log Gjirafa audio watch state for " +
                    "${request.userId}/${request.audioId}: ${e.message}",
            )
            mapOf("status" to 500)
        }

    fun assetPlays(
        assetId: String,
        from: LocalDate,
        to: LocalDate,
    ): Double =
        if (assetId.startsWith("v"))
            fetchVideoAnalyticsGraph(assetId, from, to, VideoAnalyticsGraphMetrics.VIEWS).total
        else
            fetchAudioAnalyticsGraph(assetId, from, to, AudioAnalyticsGraphMetrics.LISTENERS).total

    fun assetCompletePlays(
        assetId: String,
        from: LocalDate,
        to: LocalDate,
    ): Double =
        if (assetId.startsWith("v"))
            fetchVideoAnalyticsGraph(assetId, from, to, VideoAnalyticsGraphMetrics.COMPLETES).total
        else
            fetchAudioAnalyticsGraph(assetId, from, to, AudioAnalyticsGraphMetrics.COMPLETES).total

    fun liveViewers(livestreamId: String): Int = fetchLiveViewers(livestreamId).result.total

    fun assetAverageViewDuration(assetId: String): Double =
        if (assetId.startsWith("v"))
            videoAverageViewDuration(assetId)
        else
            audioAverageListenDuration(assetId)

    private fun fetchLiveViewers(livestreamId: String) =
        "$vpAnalyticsUrl/liveViewers"
            .httpGet(listOf("videoId" to livestreamId, "isLive" to true))
            .authorize(apiKey)
            .fetch<GjirafaResponse<LiveViewerResponse>>()

    // https://vp.gjirafa.tech/documentation/api/analytics-api/videoAnalytics/V2/getVideoCardValues
    private fun videoAverageViewDuration(videoId: String): Double =
        "$videoAnalyticsUrlV2/keyMoments/cards/$videoId"
            .httpGet(
                listOf(
                    "intervalType" to GjirafaIntervals.CUSTOM.value,
                    "startDate" to Instant.ofEpochSecond(1).atZone(ZoneOffset.UTC).toLocalDate(),
                    "endDate" to LocalDate.now().plusDays(1),
                ),
            )
            .authorize(apiKey)
            .fetch<GjirafaResponse<VideoKeyMomentsCardModelResponseModel>>()
            .result
            .averageViewDuration

    // https://vp.gjirafa.tech/documentation/api/analytics-api/audioAnalytics/getAudioKeyMomentsCardValues
    private fun audioAverageListenDuration(audioId: String): Double =
        "$audioAnalyticsUrl/keyMoments/cards"
            .httpGet(
                listOf(
                    "audioId" to audioId,
                    "intervalType" to GjirafaIntervals.CUSTOM.value,
                    "startDate" to Instant.ofEpochSecond(1).atZone(ZoneOffset.UTC).toLocalDate(),
                    "endDate" to LocalDate.now().plusDays(1),
                ),
            )
            .authorize(apiKey)
            .fetch<GjirafaResponse<AudioKeyMomentsCardModel>>()
            .result
            .averageListenTime

    // https://vp.gjirafa.tech/documentation/api/analytics-api/audioAnalytics/getGraphMetrics
    private fun fetchAudioAnalyticsGraph(
        audioId: String,
        from: LocalDate,
        to: LocalDate,
        metric: AudioAnalyticsGraphMetrics,
    ): MetricsModel =
        "$audioAnalyticsUrl/graph"
            .httpGet(
                listOf(
                    "audioId" to audioId,
                    "metric" to metric.value,
                    "startDate" to from,
                    "endDate" to to,
                    "intervalType" to GjirafaIntervals.CUSTOM.value,
                ),
            )
            .authorize(apiKey)
            .fetch<GjirafaResponse<MetricsModel>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/analytics-api/videoAnalytics/V2/getVideoGraphMetrics
    private fun fetchVideoAnalyticsGraph(
        videoId: String,
        from: LocalDate,
        to: LocalDate,
        metric: VideoAnalyticsGraphMetrics,
    ): MetricsModel =
        "$videoAnalyticsUrlV2/graph"
            .httpGet(
                listOf(
                    "videoId" to videoId,
                    "metric" to metric.value,
                    "startDate" to from,
                    "endDate" to to,
                    "intervalType" to GjirafaIntervals.CUSTOM.value,
                ),
            )
            .authorize(apiKey)
            .fetch<GjirafaResponse<MetricsModel>>()
            .result
}
