package hero.model

import com.fasterxml.jackson.annotation.JsonProperty
import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant

@NoArg
data class Subscriber(
    val userId: String,
    val creatorId: String,
    val tierId: String,
    val status: SubscriberStatus,
    val cancelAtPeriodEnd: Boolean = false,
    val subscribed: Instant,
    val expires: Instant? = null,
    val cancelAt: Instant? = null,
    val cancelledAt: Instant? = null,
    val cancelledBy: String? = null,
    val cancelledByRole: CancelledByRole? = null,
    val cancelledReason: String? = null,
    @Deprecated("Use `cancelledReason`")
    val refusedReason: String? = null,
    val refused: Boolean = false,
    val subscriberType: SubscriberType = SubscriberType.STRIPE,
    val couponId: String? = null,
    /** number of months the applied coupon is active - must be null when `days` are used */
    val couponAppliedForMonths: Long? = null,
    /** number of days the applied coupon is active - must be null when `months` are used */
    val couponAppliedForDays: Long? = null,
    /** timestamp when the coupon application on this subscription ends (based on months/days above) */
    val couponExpiresAt: Instant? = null,
    val couponExpiresNotifiedAt: Instant? = null,
    val couponPercentOff: Long? = null,
    val couponMethod: CouponMethod? = null,
    val couponCampaign: String? = null,
    val refunded: Boolean = false,
    val resubscribedAt: Instant? = null,
    val id: String = "$userId|$creatorId",
    val appleTransactionId: String? = null,
) {
    companion object : EntityCollection<Subscriber> {
        override val collectionName: String = "subscribers"
    }
}

enum class CancelledByRole {
    MODERATOR,
    CREATOR,
    USER,
}

enum class SubscriberStatus(val isActive: Boolean) {
    @JsonProperty("incomplete")
    INCOMPLETE(false),
    @JsonProperty("incomplete_expired")
    INCOMPLETE_EXPIRED(false),
    @JsonProperty("trialing")
    TRIALING(true),
    @JsonProperty("active")
    ACTIVE(true),
    // https://gitlab.com/heroheroco/backend/-/issues/197
    // PAST_DUE is considered to be active subscription
    @JsonProperty("past_due")
    PAST_DUE(true),
    @JsonProperty("cancelled")
    CANCELLED(false),
    @JsonProperty("unpaid")
    UNPAID(false),
    ;

    companion object {
        val activeStatuses: List<SubscriberStatus> = entries.filter { it.isActive }
        val inactiveStatuses: List<SubscriberStatus> = entries - activeStatuses

        fun of(stripeValue: String): SubscriberStatus =
            SubscriberStatus.valueOf(
                stripeValue.replace("canceled", "cancelled").uppercase(),
            )
    }
}

enum class CancelReason {
    @JsonProperty("manual_by_support_with_refund")
    MANUAL_BY_SUPPORT_WITH_REFUND,
    @JsonProperty("manual_by_support_without_refund")
    MANUAL_BY_SUPPORT_WITHOUT_REFUND,
    @JsonProperty("manual_by_user_on_past_due")
    MANUAL_BY_USER_ON_PAST_DUE,
    @JsonProperty("manual_by_user_on_profile_cancel")
    MANUAL_BY_USER_ON_PROFILE_CANCEL,
    @JsonProperty("automatic_on_period_end")
    AUTOMATIC_ON_PERIOD_END,
    @JsonProperty("automatic_on_past_due")
    AUTOMATIC_ON_PAST_DUE,
}

enum class SubscriberType {
    @JsonProperty("stripe")
    STRIPE,
}

/**
 * Describes relation between a user and a target user
 */
enum class SubscriptionRelationType {
    /**
     * Relation of user to himself
     */
    HIMSELF,

    /**
     * User is subscribed to the target
     */
    IS_SUBSCRIBED_TO,

    /**
     * User is subscribed to the same creator as the target
     */
    IS_SUBSCRIBED_TO_SAME_CREATOR,

    /**
     * Is subscribed by the target
     */
    IS_SUBSCRIBED_BY,

    /**
     * There are multiple users in the relation
     */
    GROUP,
}
